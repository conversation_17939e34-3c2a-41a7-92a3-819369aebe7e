// WebSocket客户端管理器 - 复用Qt项目的实时通信逻辑
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.heartbeatInterval = null;
        this.lastHeartbeat = null;
        
        // 事件监听器
        this.eventListeners = new Map();
        
        // 连接统计
        this.stats = {
            connectTime: null,
            disconnectTime: null,
            messagesReceived: 0,
            messagesSent: 0,
            reconnectCount: 0
        };
        
        // 自动连接
        this.connect();
    }

    // 连接WebSocket服务器
    connect() {
        try {
            console.log('🔗 正在连接WebSocket服务器...');
            
            // 创建Socket.io连接
            this.socket = io(CONFIG.websocket.url, CONFIG.websocket.options);
            
            // 设置事件监听器
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
            this.handleConnectionError(error);
        }
    }

    // 设置事件处理器
    setupEventHandlers() {
        // 连接成功
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.stats.connectTime = new Date();
            
            console.log('✅ WebSocket连接成功');
            this.emit('connected');
            
            // 更新应用连接状态
            if (window.app) {
                window.app.updateConnectionStatus(true, '已连接');
            }
            
            // 开始心跳检测
            this.startHeartbeat();
            
            // 订阅所有节点数据
            this.subscribeToAllNodes();
        });

        // 连接断开
        this.socket.on('disconnect', (reason) => {
            this.isConnected = false;
            this.stats.disconnectTime = new Date();
            
            console.log('📱 WebSocket连接断开:', reason);
            this.emit('disconnected', reason);
            
            // 更新应用连接状态
            if (window.app) {
                window.app.updateConnectionStatus(false, '连接断开');
            }
            
            // 停止心跳检测
            this.stopHeartbeat();
            
            // 尝试重连
            this.handleReconnect(reason);
        });

        // 连接错误
        this.socket.on('connect_error', (error) => {
            console.error('❌ WebSocket连接错误:', error);
            this.emit('error', error);
            this.handleConnectionError(error);
        });

        // 接收初始数据
        this.socket.on('initial-data', (data) => {
            console.log('📊 收到初始数据:', data);
            this.stats.messagesReceived++;
            this.emit('initialData', data);
            
            // 更新应用状态
            if (window.app) {
                window.app.setState({
                    nodes: data.nodes,
                    systemStatus: data.systemStatus,
                    alerts: data.alerts
                });
            }
        });

        // 接收数据更新
        this.socket.on('data-update', (data) => {
            this.stats.messagesReceived++;
            this.emit('dataUpdate', data);
            
            // 更新应用状态
            if (window.app) {
                const currentState = window.app.getState();
                window.app.setState({
                    nodes: data.nodes,
                    lastUpdate: data.timestamp
                });
            }
        });

        // 接收系统状态更新
        this.socket.on('system-status-update', (data) => {
            this.stats.messagesReceived++;
            this.emit('systemStatusUpdate', data);
            
            // 更新应用状态
            if (window.app) {
                window.app.setState({ systemStatus: data });
            }
        });

        // 接收警告触发
        this.socket.on('alert-trigger', (data) => {
            this.stats.messagesReceived++;
            this.emit('alertTrigger', data);
            
            console.log('🚨 收到警告:', data.alerts);
            
            // 更新应用状态
            if (window.app) {
                const currentState = window.app.getState();
                const newAlerts = [...(currentState.alerts || []), ...data.alerts];
                window.app.setState({ alerts: newAlerts });
            }
        });

        // 心跳响应
        this.socket.on('pong', (data) => {
            this.lastHeartbeat = new Date();
            this.emit('heartbeat', data);
        });

        // 连接确认
        this.socket.on('connection-confirmed', (data) => {
            console.log('✅ 连接确认:', data);
            this.emit('connectionConfirmed', data);
        });
    }

    // 订阅所有节点数据
    subscribeToAllNodes() {
        if (this.isConnected) {
            this.socket.emit('subscribe-all');
            console.log('📡 已订阅所有节点数据');
        }
    }

    // 订阅特定节点
    subscribeToNode(nodeId) {
        if (this.isConnected) {
            this.socket.emit('subscribe-node', nodeId);
            console.log(`📡 已订阅节点${nodeId}数据`);
        }
    }

    // 取消订阅节点
    unsubscribeFromNode(nodeId) {
        if (this.isConnected) {
            this.socket.emit('unsubscribe-node', nodeId);
            console.log(`📡 已取消订阅节点${nodeId}数据`);
        }
    }

    // 开始心跳检测
    startHeartbeat() {
        this.stopHeartbeat(); // 确保没有重复的心跳
        
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.socket.emit('ping');
                this.stats.messagesSent++;
            }
        }, 30000); // 30秒心跳间隔
        
        console.log('💓 心跳检测已启动');
    }

    // 停止心跳检测
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
            console.log('💓 心跳检测已停止');
        }
    }

    // 处理重连
    handleReconnect(reason) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.stats.reconnectCount++;
            
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
            
            console.log(`🔄 ${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
            
            // 更新应用连接状态
            if (window.app) {
                window.app.updateConnectionStatus(false, `重连中(${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            }
            
            setTimeout(() => {
                if (!this.isConnected) {
                    this.connect();
                }
            }, delay);
        } else {
            console.error('❌ 重连次数超限，停止重连');
            this.emit('maxReconnectAttemptsReached');
            
            // 更新应用连接状态
            if (window.app) {
                window.app.updateConnectionStatus(false, '连接失败');
            }
        }
    }

    // 处理连接错误
    handleConnectionError(error) {
        console.error('❌ 连接错误处理:', error);
        
        // 更新应用连接状态
        if (window.app) {
            window.app.updateConnectionStatus(false, '连接错误');
        }
    }

    // 发送消息
    send(event, data) {
        if (this.isConnected && this.socket) {
            this.socket.emit(event, data);
            this.stats.messagesSent++;
            return true;
        } else {
            console.warn('⚠️ WebSocket未连接，无法发送消息');
            return false;
        }
    }

    // 添加事件监听器
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    // 移除事件监听器
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    // 触发事件
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`❌ 事件处理器错误 [${event}]:`, error);
                }
            });
        }
    }

    // 获取连接状态
    getStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            lastHeartbeat: this.lastHeartbeat,
            stats: this.stats
        };
    }

    // 断开连接
    disconnect() {
        if (this.socket) {
            this.stopHeartbeat();
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            console.log('🔌 WebSocket连接已断开');
        }
    }

    // 重置连接
    reset() {
        this.disconnect();
        this.reconnectAttempts = 0;
        this.stats = {
            connectTime: null,
            disconnectTime: null,
            messagesReceived: 0,
            messagesSent: 0,
            reconnectCount: 0
        };
        this.connect();
    }
}

// 导出WebSocket管理器
window.WebSocketManager = WebSocketManager;
