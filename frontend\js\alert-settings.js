// 警告设置页面管理器
class AlertSettingsManager {
    constructor() {
        this.currentSettings = null;
        this.defaultSettings = this.getDefaultSettings();
        
        // 初始化
        this.init();
    }

    // 初始化
    init() {
        console.log('⚙️ 初始化警告设置页面...');
        
        // 初始化DOM元素
        this.initDOMElements();
        
        // 初始化主题
        this.initTheme();
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 加载当前设置
        this.loadCurrentSettings();
        
        // 更新时间显示
        this.updateTime();
        setInterval(() => {
            this.updateTime();
        }, 1000);
        
        console.log('✅ 警告设置页面初始化完成');
    }

    // 初始化DOM元素
    initDOMElements() {
        // 基础元素
        this.backButton = Utils.dom.$('#backButton');
        this.themeToggle = Utils.dom.$('#themeToggle');
        this.currentTime = Utils.dom.$('#currentTime');
        
        // 通知设置
        this.browserNotifications = Utils.dom.$('#browserNotifications');
        this.soundAlerts = Utils.dom.$('#soundAlerts');
        this.popupAlerts = Utils.dom.$('#popupAlerts');
        this.emailAlerts = Utils.dom.$('#emailAlerts');
        
        // 阈值设置
        this.thresholdInputs = {
            temperature: {
                min: Utils.dom.$('#tempMin'),
                max: Utils.dom.$('#tempMax'),
                range: Utils.dom.$('#tempRange')
            },
            humidity: {
                min: Utils.dom.$('#humiMin'),
                max: Utils.dom.$('#humiMax'),
                range: Utils.dom.$('#humiRange')
            },
            light: {
                min: Utils.dom.$('#lightMin'),
                max: Utils.dom.$('#lightMax'),
                range: Utils.dom.$('#lightRange')
            },
            smoke: {
                min: Utils.dom.$('#smokeMin'),
                max: Utils.dom.$('#smokeMax'),
                range: Utils.dom.$('#smokeRange')
            }
        };
        
        // 操作按钮
        this.saveButton = Utils.dom.$('#saveSettings');
        this.resetButton = Utils.dom.$('#resetSettings');
        this.exportButton = Utils.dom.$('#exportSettings');
        this.importButton = Utils.dom.$('#importSettings');
        this.importFile = Utils.dom.$('#importFile');
        
        // 测试按钮
        this.testButtons = Utils.dom.$$('.test-btn');
        
        console.log('📱 DOM元素初始化完成');
    }

    // 初始化主题
    initTheme() {
        Utils.theme.init();
        console.log('🎨 主题初始化完成');
    }

    // 初始化事件监听器
    initEventListeners() {
        // 返回按钮
        if (this.backButton) {
            this.backButton.addEventListener('click', () => {
                this.goBack();
            });
        }

        // 主题切换
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                Utils.theme.toggle();
            });
        }

        // 阈值输入监听
        Object.keys(this.thresholdInputs).forEach(sensorType => {
            const inputs = this.thresholdInputs[sensorType];
            
            if (inputs.min) {
                inputs.min.addEventListener('input', () => {
                    this.updateThresholdPreview(sensorType);
                });
            }
            
            if (inputs.max) {
                inputs.max.addEventListener('input', () => {
                    this.updateThresholdPreview(sensorType);
                });
            }
        });

        // 操作按钮
        if (this.saveButton) {
            this.saveButton.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        if (this.resetButton) {
            this.resetButton.addEventListener('click', () => {
                this.resetToDefault();
            });
        }

        if (this.exportButton) {
            this.exportButton.addEventListener('click', () => {
                this.exportSettings();
            });
        }

        if (this.importButton) {
            this.importButton.addEventListener('click', () => {
                this.importFile.click();
            });
        }

        if (this.importFile) {
            this.importFile.addEventListener('change', (e) => {
                this.importSettings(e.target.files[0]);
            });
        }

        // 测试按钮
        this.testButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const level = btn.dataset.level;
                this.testAlert(level);
            });
        });

        console.log('👂 事件监听器初始化完成');
    }

    // 获取默认设置
    getDefaultSettings() {
        return {
            notifications: {
                browserNotifications: true,
                soundAlerts: true,
                popupAlerts: true,
                emailAlerts: false
            },
            thresholds: {
                temperature: { min: 0, max: 50 },
                humidity: { min: 10, max: 500 },
                light: { min: 10, max: 800 },
                smoke: { min: 0, max: 600 }
            }
        };
    }

    // 加载当前设置
    loadCurrentSettings() {
        // 从本地存储加载设置
        const savedSettings = Utils.storage.get('alertSettings');
        this.currentSettings = savedSettings || this.defaultSettings;
        
        // 更新UI
        this.updateUI();
        
        console.log('📥 当前设置已加载:', this.currentSettings);
    }

    // 更新UI
    updateUI() {
        // 更新通知设置
        if (this.browserNotifications) {
            this.browserNotifications.checked = this.currentSettings.notifications.browserNotifications;
        }
        if (this.soundAlerts) {
            this.soundAlerts.checked = this.currentSettings.notifications.soundAlerts;
        }
        if (this.popupAlerts) {
            this.popupAlerts.checked = this.currentSettings.notifications.popupAlerts;
        }
        if (this.emailAlerts) {
            this.emailAlerts.checked = this.currentSettings.notifications.emailAlerts;
        }

        // 更新阈值设置
        Object.keys(this.currentSettings.thresholds).forEach(sensorType => {
            const threshold = this.currentSettings.thresholds[sensorType];
            const inputs = this.thresholdInputs[sensorType];
            
            if (inputs && inputs.min && inputs.max) {
                inputs.min.value = threshold.min;
                inputs.max.value = threshold.max;
                this.updateThresholdPreview(sensorType);
            }
        });
    }

    // 更新阈值预览
    updateThresholdPreview(sensorType) {
        const inputs = this.thresholdInputs[sensorType];
        if (!inputs || !inputs.min || !inputs.max || !inputs.range) return;

        const min = parseFloat(inputs.min.value);
        const max = parseFloat(inputs.max.value);
        const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
        
        if (min >= max) {
            inputs.range.textContent = '无效范围';
            inputs.range.style.color = 'var(--error-color)';
        } else {
            inputs.range.textContent = `${min}${sensorConfig.unit} - ${max}${sensorConfig.unit}`;
            inputs.range.style.color = 'var(--text-primary)';
        }
    }

    // 收集当前设置
    collectCurrentSettings() {
        const settings = {
            notifications: {
                browserNotifications: this.browserNotifications?.checked || false,
                soundAlerts: this.soundAlerts?.checked || false,
                popupAlerts: this.popupAlerts?.checked || false,
                emailAlerts: this.emailAlerts?.checked || false
            },
            thresholds: {}
        };

        // 收集阈值设置
        Object.keys(this.thresholdInputs).forEach(sensorType => {
            const inputs = this.thresholdInputs[sensorType];
            if (inputs && inputs.min && inputs.max) {
                const min = parseFloat(inputs.min.value);
                const max = parseFloat(inputs.max.value);
                
                if (min < max) {
                    settings.thresholds[sensorType] = { min, max };
                }
            }
        });

        return settings;
    }

    // 验证设置
    validateSettings(settings) {
        const errors = [];

        // 验证阈值
        Object.keys(settings.thresholds).forEach(sensorType => {
            const threshold = settings.thresholds[sensorType];
            
            if (threshold.min >= threshold.max) {
                errors.push(`${sensorType}的最小值必须小于最大值`);
            }
            
            if (threshold.min < 0 && sensorType !== 'temperature') {
                errors.push(`${sensorType}的最小值不能为负数`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 保存设置
    saveSettings() {
        const newSettings = this.collectCurrentSettings();
        const validation = this.validateSettings(newSettings);
        
        if (!validation.isValid) {
            alert('设置验证失败:\n' + validation.errors.join('\n'));
            return;
        }

        // 保存到本地存储
        Utils.storage.set('alertSettings', newSettings);
        this.currentSettings = newSettings;
        
        // 应用设置到警告管理器
        this.applySettingsToAlertManager();
        
        console.log('💾 设置已保存:', newSettings);
        alert('设置保存成功！');
    }

    // 应用设置到警告管理器
    applySettingsToAlertManager() {
        // 如果主页面的警告管理器存在，更新其设置
        if (window.opener && window.opener.app) {
            const alertManager = window.opener.app.getManager('alerts');
            if (alertManager) {
                alertManager.updateNotificationSettings(this.currentSettings.notifications);
                alertManager.updateAlertConfig(this.currentSettings.thresholds);
            }
        }
    }

    // 重置为默认设置
    resetToDefault() {
        if (confirm('确定要重置为默认设置吗？这将清除所有自定义配置。')) {
            this.currentSettings = Utils.deepClone(this.defaultSettings);
            this.updateUI();
            console.log('🔄 设置已重置为默认值');
        }
    }

    // 导出设置
    exportSettings() {
        const exportData = {
            exportTime: new Date().toISOString(),
            version: '1.0',
            settings: this.currentSettings
        };

        const filename = `alert_settings_${this.getTimestamp()}.json`;
        const jsonString = JSON.stringify(exportData, null, 2);
        
        this.downloadFile(jsonString, filename, 'application/json');
        
        console.log(`📄 设置已导出: ${filename}`);
    }

    // 导入设置
    importSettings(file) {
        if (!file) return;

        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                const importData = JSON.parse(e.target.result);
                
                if (importData.settings) {
                    this.currentSettings = importData.settings;
                    this.updateUI();
                    console.log('📥 设置已导入');
                    alert('设置导入成功！');
                } else {
                    alert('导入文件格式无效');
                }
            } catch (error) {
                console.error('❌ 导入设置失败:', error);
                alert('导入失败：文件格式错误');
            }
        };
        
        reader.readAsText(file);
    }

    // 测试警告
    testAlert(level) {
        const testMessages = {
            error: '这是一个错误级别的测试警告',
            warning: '这是一个警告级别的测试警告',
            info: '这是一个信息级别的测试警告'
        };

        // 创建测试警告
        const testAlert = {
            nodeId: 1,
            type: 'test',
            level: level,
            message: testMessages[level],
            value: 999,
            threshold: { min: 0, max: 100 }
        };

        // 如果主页面的警告管理器存在，添加测试警告
        if (window.opener && window.opener.app) {
            const alertManager = window.opener.app.getManager('alerts');
            if (alertManager) {
                alertManager.addAlert(testAlert);
            }
        }

        console.log(`🧪 测试${level}级别警告`);
    }

    // 返回主页
    goBack() {
        window.close();
    }

    // 更新时间显示
    updateTime() {
        if (this.currentTime) {
            this.currentTime.textContent = Utils.formatTime();
        }
    }

    // 下载文件
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 100);
    }

    // 获取时间戳
    getTimestamp() {
        return new Date().toISOString()
            .replace(/[:.]/g, '-')
            .replace('T', '_')
            .split('.')[0];
    }
}

// 当DOM加载完成后初始化警告设置管理器
document.addEventListener('DOMContentLoaded', () => {
    window.alertSettingsManager = new AlertSettingsManager();
});
