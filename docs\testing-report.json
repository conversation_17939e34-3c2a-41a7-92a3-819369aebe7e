{"summary": {"totalTests": 21, "passedTests": 19, "failedTests": 2, "successRate": "90.5%", "totalTime": "1167ms", "timestamp": "2025-06-25T13:28:13.811Z"}, "results": [{"name": "主页面加载", "success": true, "message": "状态码: 200, 响应时间: 9ms", "timestamp": "2025-06-25T13:28:12.657Z"}, {"name": "节点详情页面", "success": true, "message": "状态码: 200, 响应时间: 3ms", "timestamp": "2025-06-25T13:28:12.661Z"}, {"name": "警告设置页面", "success": true, "message": "状态码: 200, 响应时间: 2ms", "timestamp": "2025-06-25T13:28:12.664Z"}, {"name": "CSS资源加载", "success": true, "message": "状态码: 200, 响应时间: 4ms", "timestamp": "2025-06-25T13:28:12.668Z"}, {"name": "JavaScript资源加载", "success": true, "message": "状态码: 200, 响应时间: 4ms", "timestamp": "2025-06-25T13:28:12.673Z"}, {"name": "响应时间测试", "success": true, "message": "平均响应时间: 2.80ms (10次测试)", "timestamp": "2025-06-25T13:28:12.702Z"}, {"name": "并发连接测试", "success": true, "message": "并发20个请求，成功率: 100.0%", "timestamp": "2025-06-25T13:28:12.728Z"}, {"name": "内存使用测试", "success": true, "message": "堆内存使用: 6MB, RSS: 45MB", "timestamp": "2025-06-25T13:28:12.728Z"}, {"name": "健康检查API", "success": true, "message": "状态码: 200, 响应时间: 2ms", "timestamp": "2025-06-25T13:28:12.731Z"}, {"name": "节点数据API", "success": true, "message": "状态码: 200, 响应时间: 2ms", "timestamp": "2025-06-25T13:28:12.733Z"}, {"name": "系统状态API", "success": false, "message": "状态码: 404, 响应时间: 1ms", "timestamp": "2025-06-25T13:28:12.734Z"}, {"name": "WebSocket连接", "success": false, "message": "WebSocket连接失败: socket hang up", "timestamp": "2025-06-25T13:28:13.754Z"}, {"name": "JS文件: /js/config.js", "success": true, "message": "状态码: 200, 响应时间: 4ms", "timestamp": "2025-06-25T13:28:13.760Z"}, {"name": "JS文件: /js/utils.js", "success": true, "message": "状态码: 200, 响应时间: 6ms", "timestamp": "2025-06-25T13:28:13.766Z"}, {"name": "JS文件: /js/app.js", "success": true, "message": "状态码: 200, 响应时间: 5ms", "timestamp": "2025-06-25T13:28:13.771Z"}, {"name": "JS文件: /js/websocket.js", "success": true, "message": "状态码: 200, 响应时间: 5ms", "timestamp": "2025-06-25T13:28:13.776Z"}, {"name": "JS文件: /js/nodes.js", "success": true, "message": "状态码: 200, 响应时间: 6ms", "timestamp": "2025-06-25T13:28:13.783Z"}, {"name": "JS文件: /js/alerts.js", "success": true, "message": "状态码: 200, 响应时间: 9ms", "timestamp": "2025-06-25T13:28:13.794Z"}, {"name": "JS文件: /js/charts.js", "success": true, "message": "状态码: 200, 响应时间: 6ms", "timestamp": "2025-06-25T13:28:13.801Z"}, {"name": "CSS文件: /css/styles.css", "success": true, "message": "状态码: 200, 响应时间: 5ms", "timestamp": "2025-06-25T13:28:13.807Z"}, {"name": "CSS文件: /css/components.css", "success": true, "message": "状态码: 200, 响应时间: 3ms", "timestamp": "2025-06-25T13:28:13.811Z"}]}