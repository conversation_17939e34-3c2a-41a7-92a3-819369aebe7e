/****************************************************************************
** Meta object code from reading C++ file 'qmqtt_networkinterface.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../mqtt/qmqtt_networkinterface.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'qmqtt_networkinterface.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QMQTT__NetworkInterface_t {
    QByteArrayData data[19];
    char stringdata0[226];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QMQTT__NetworkInterface_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QMQTT__NetworkInterface_t qt_meta_stringdata_QMQTT__NetworkInterface = {
    {
QT_MOC_LITERAL(0, 0, 23), // "QMQTT::NetworkInterface"
QT_MOC_LITERAL(1, 24, 9), // "connected"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 12), // "disconnected"
QT_MOC_LITERAL(4, 48, 8), // "received"
QT_MOC_LITERAL(5, 57, 12), // "QMQTT::Frame"
QT_MOC_LITERAL(6, 70, 5), // "frame"
QT_MOC_LITERAL(7, 76, 5), // "error"
QT_MOC_LITERAL(8, 82, 28), // "QAbstractSocket::SocketError"
QT_MOC_LITERAL(9, 111, 9), // "sslErrors"
QT_MOC_LITERAL(10, 121, 16), // "QList<QSslError>"
QT_MOC_LITERAL(11, 138, 6), // "errors"
QT_MOC_LITERAL(12, 145, 13), // "connectToHost"
QT_MOC_LITERAL(13, 159, 12), // "QHostAddress"
QT_MOC_LITERAL(14, 172, 4), // "host"
QT_MOC_LITERAL(15, 177, 4), // "port"
QT_MOC_LITERAL(16, 182, 8), // "hostName"
QT_MOC_LITERAL(17, 191, 18), // "disconnectFromHost"
QT_MOC_LITERAL(18, 210, 15) // "ignoreSslErrors"

    },
    "QMQTT::NetworkInterface\0connected\0\0"
    "disconnected\0received\0QMQTT::Frame\0"
    "frame\0error\0QAbstractSocket::SocketError\0"
    "sslErrors\0QList<QSslError>\0errors\0"
    "connectToHost\0QHostAddress\0host\0port\0"
    "hostName\0disconnectFromHost\0ignoreSslErrors"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QMQTT__NetworkInterface[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   59,    2, 0x06 /* Public */,
       3,    0,   60,    2, 0x06 /* Public */,
       4,    1,   61,    2, 0x06 /* Public */,
       7,    1,   64,    2, 0x06 /* Public */,
       9,    1,   67,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    2,   70,    2, 0x0a /* Public */,
      12,    2,   75,    2, 0x0a /* Public */,
      17,    0,   80,    2, 0x0a /* Public */,
      18,    0,   81,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, 0x80000000 | 8,    7,
    QMetaType::Void, 0x80000000 | 10,   11,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 13, QMetaType::UShort,   14,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::UShort,   16,   15,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void QMQTT::NetworkInterface::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<NetworkInterface *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->connected(); break;
        case 1: _t->disconnected(); break;
        case 2: _t->received((*reinterpret_cast< const QMQTT::Frame(*)>(_a[1]))); break;
        case 3: _t->error((*reinterpret_cast< QAbstractSocket::SocketError(*)>(_a[1]))); break;
        case 4: _t->sslErrors((*reinterpret_cast< const QList<QSslError>(*)>(_a[1]))); break;
        case 5: _t->connectToHost((*reinterpret_cast< const QHostAddress(*)>(_a[1])),(*reinterpret_cast< const quint16(*)>(_a[2]))); break;
        case 6: _t->connectToHost((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const quint16(*)>(_a[2]))); break;
        case 7: _t->disconnectFromHost(); break;
        case 8: _t->ignoreSslErrors(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractSocket::SocketError >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QList<QSslError> >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (NetworkInterface::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkInterface::connected)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (NetworkInterface::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkInterface::disconnected)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (NetworkInterface::*)(const QMQTT::Frame & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkInterface::received)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (NetworkInterface::*)(QAbstractSocket::SocketError );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkInterface::error)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (NetworkInterface::*)(const QList<QSslError> & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkInterface::sslErrors)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject QMQTT::NetworkInterface::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_QMQTT__NetworkInterface.data,
    qt_meta_data_QMQTT__NetworkInterface,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QMQTT::NetworkInterface::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QMQTT::NetworkInterface::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QMQTT__NetworkInterface.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QMQTT::NetworkInterface::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void QMQTT::NetworkInterface::connected()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void QMQTT::NetworkInterface::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void QMQTT::NetworkInterface::received(const QMQTT::Frame & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QMQTT::NetworkInterface::error(QAbstractSocket::SocketError _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QMQTT::NetworkInterface::sslErrors(const QList<QSslError> & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
