// 节点管理器 - 复用Qt项目的节点控制逻辑
class NodeManager {
    constructor() {
        this.nodes = new Map(); // 存储节点数据
        this.nodesContainer = null;
        this.updateInterval = CONFIG.nodes.updateInterval; // 50ms更新间隔
        this.offlineTimeout = CONFIG.nodes.offlineTimeout; // 5秒离线超时
        
        // 初始化
        this.init();
    }

    // 初始化节点管理器
    init() {
        this.nodesContainer = Utils.dom.$('#nodesGrid');
        
        // 初始化三个节点 - 复用Qt项目设置
        for (let i = 1; i <= CONFIG.nodes.count; i++) {
            this.createNode(i, CONFIG.nodes.names[i - 1]);
        }
        
        console.log('📍 节点管理器初始化完成');
    }

    // 创建节点
    createNode(id, name) {
        const nodeData = {
            id,
            name,
            status: false, // 复用Qt的flag1-3逻辑
            lastUpdate: null,
            currentData: {
                temperature: 0, // 复用Qt的temp1-3
                humidity: 0,    // 复用Qt的humi1-3
                light: 0,       // 复用Qt的light1-3
                smoke: 0        // 复用Qt的smog1-3
            },
            alertLevel: 'normal'
        };
        
        this.nodes.set(id, nodeData);
        this.renderNode(nodeData);
        
        console.log(`📍 创建节点${id}: ${name}`);
    }

    // 渲染节点卡片
    renderNode(nodeData) {
        if (!this.nodesContainer) return;

        const nodeCard = Utils.dom.create('div', {
            className: 'node-card fade-in',
            id: `node-${nodeData.id}`
        });

        nodeCard.innerHTML = `
            <div class="node-header">
                <h3 class="node-title">${nodeData.name}</h3>
                <div class="node-status ${nodeData.status ? 'online' : 'offline'}">
                    <span class="node-status-dot"></span>
                    <span class="status-text">${nodeData.status ? '在线' : '离线'}</span>
                </div>
            </div>
            <div class="sensor-grid">
                <div class="sensor-item" data-sensor="temperature">
                    <div class="sensor-label">温度</div>
                    <div class="sensor-value">${Utils.formatSensorValue('temperature', nodeData.currentData.temperature)}</div>
                </div>
                <div class="sensor-item" data-sensor="humidity">
                    <div class="sensor-label">湿度</div>
                    <div class="sensor-value">${Utils.formatSensorValue('humidity', nodeData.currentData.humidity)}</div>
                </div>
                <div class="sensor-item" data-sensor="light">
                    <div class="sensor-label">光照</div>
                    <div class="sensor-value">${Utils.formatSensorValue('light', nodeData.currentData.light)}</div>
                </div>
                <div class="sensor-item" data-sensor="smoke">
                    <div class="sensor-label">烟雾</div>
                    <div class="sensor-value">${Utils.formatSensorValue('smoke', nodeData.currentData.smoke)}</div>
                </div>
            </div>
            <div class="node-footer">
                <div class="last-update">
                    最后更新: ${nodeData.lastUpdate ? Utils.formatRelativeTime(nodeData.lastUpdate) : '从未'}
                </div>
            </div>
        `;

        // 添加点击事件
        nodeCard.addEventListener('click', () => {
            this.navigateToNodeDetail(nodeData.id);
        });

        // 添加双击事件进入详情页
        nodeCard.addEventListener('dblclick', () => {
            this.navigateToNodeDetail(nodeData.id);
        });

        this.nodesContainer.appendChild(nodeCard);
    }

    // 更新节点数据
    updateNode(nodeId, data) {
        const node = this.nodes.get(nodeId);
        if (!node) {
            console.warn(`⚠️ 节点${nodeId}不存在`);
            return;
        }

        // 更新节点数据
        node.currentData = { ...data };
        node.lastUpdate = new Date();
        
        // 检查节点状态 - 复用Qt逻辑：所有数据为0视为离线
        const isOnline = !(
            data.temperature === 0 && 
            data.humidity === 0 && 
            data.light === 0 && 
            data.smoke === 0
        );
        
        node.status = isOnline;

        // 检查警告级别
        node.alertLevel = this.checkAlertLevel(data);

        // 更新UI
        this.updateNodeUI(nodeId);

        console.log(`📊 节点${nodeId}数据更新:`, data);
    }

    // 检查警告级别
    checkAlertLevel(data) {
        // 复用Qt项目的警告逻辑
        const limits = CONFIG.sensorLimits;
        
        // 检查是否有错误级别的警告
        if (data.temperature > limits.temperature.max || data.temperature < limits.temperature.min ||
            data.humidity > limits.humidity.max || data.humidity < limits.humidity.min ||
            data.smoke > limits.smoke.max) {
            return 'error';
        }
        
        // 检查是否有警告级别的警告
        if (data.light > limits.light.max || data.light < limits.light.min) {
            return 'warning';
        }
        
        return 'normal';
    }

    // 更新节点UI
    updateNodeUI(nodeId) {
        const node = this.nodes.get(nodeId);
        if (!node) return;

        const nodeCard = Utils.dom.$(`#node-${nodeId}`);
        if (!nodeCard) return;

        // 更新状态
        const statusElement = nodeCard.querySelector('.node-status');
        const statusText = nodeCard.querySelector('.status-text');
        
        if (statusElement && statusText) {
            Utils.dom.removeClass(statusElement, 'online', 'offline');
            Utils.dom.addClass(statusElement, node.status ? 'online' : 'offline');
            statusText.textContent = node.status ? '在线' : '离线';
        }

        // 更新传感器数据
        const sensorItems = nodeCard.querySelectorAll('.sensor-item');
        sensorItems.forEach(item => {
            const sensorType = item.dataset.sensor;
            const valueElement = item.querySelector('.sensor-value');
            
            if (valueElement && node.currentData[sensorType] !== undefined) {
                const newValue = Utils.formatSensorValue(sensorType, node.currentData[sensorType]);
                
                // 如果值发生变化，添加更新动画
                if (valueElement.textContent !== newValue) {
                    Utils.dom.addClass(item, 'data-update');
                    valueElement.textContent = newValue;
                    
                    setTimeout(() => {
                        Utils.dom.removeClass(item, 'data-update');
                    }, 600);
                }
            }
        });

        // 更新最后更新时间
        const lastUpdateElement = nodeCard.querySelector('.last-update');
        if (lastUpdateElement && node.lastUpdate) {
            lastUpdateElement.textContent = `最后更新: ${Utils.formatRelativeTime(node.lastUpdate)}`;
        }

        // 更新警告级别样式
        Utils.dom.removeClass(nodeCard, 'alert-normal', 'alert-warning', 'alert-error');
        Utils.dom.addClass(nodeCard, `alert-${node.alertLevel}`);
    }

    // 批量更新节点数据
    updateNodes(nodesData) {
        if (!Array.isArray(nodesData)) return;

        nodesData.forEach(nodeData => {
            if (nodeData.id && nodeData.currentData) {
                this.updateNode(nodeData.id, nodeData.currentData);
            }
        });
    }

    // 跳转到节点详情页面
    navigateToNodeDetail(nodeId) {
        const node = this.nodes.get(nodeId);
        if (!node) {
            console.warn(`⚠️ 节点${nodeId}不存在`);
            return;
        }

        console.log(`🔍 跳转到节点${nodeId}详情页面`);

        // 跳转到节点详情页面
        window.location.href = `pages/node-detail.html?nodeId=${nodeId}`;
    }

    // 显示节点详情（保留原方法用于快速查看）
    showNodeDetails(nodeId) {
        const node = this.nodes.get(nodeId);
        if (!node) return;

        console.log(`🔍 显示节点${nodeId}详情:`, node);

        // 创建详情弹窗
        this.showNodeDetailsModal(node);
    }

    // 显示节点详情模态框
    showNodeDetailsModal(node) {
        const modal = Utils.dom.create('div', {
            className: 'node-details-modal'
        });

        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>节点${node.id}详情</h3>
                    <button class="modal-close" onclick="this.closest('.node-details-modal').remove()">✕</button>
                </div>
                <div class="modal-body">
                    <div class="detail-item">
                        <span class="detail-label">节点名称:</span>
                        <span class="detail-value">${node.name}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">状态:</span>
                        <span class="detail-value ${node.status ? 'online' : 'offline'}">${node.status ? '在线' : '离线'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">最后更新:</span>
                        <span class="detail-value">${node.lastUpdate ? Utils.formatRelativeTime(node.lastUpdate) : '从未'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">温度:</span>
                        <span class="detail-value">${node.currentData.temperature}°C</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">湿度:</span>
                        <span class="detail-value">${node.currentData.humidity}%RH</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">光照:</span>
                        <span class="detail-value">${node.currentData.light}lm</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">烟雾:</span>
                        <span class="detail-value">${node.currentData.smoke}mg</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">警告级别:</span>
                        <span class="detail-value alert-${node.alertLevel}">${this.getAlertLevelText(node.alertLevel)}</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.node-details-modal').remove()">关闭</button>
                    <button class="btn-primary" onclick="window.nodeManager.navigateToNodeDetail(${node.id})">查看详情</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加动画
        setTimeout(() => {
            Utils.dom.addClass(modal, 'show');
        }, 10);
    }

    // 获取警告级别文本
    getAlertLevelText(level) {
        const texts = {
            normal: '正常',
            warning: '警告',
            error: '错误'
        };
        return texts[level] || '未知';
    }

    // 获取节点数据
    getNode(nodeId) {
        return this.nodes.get(nodeId);
    }

    // 获取所有节点数据
    getAllNodes() {
        return Array.from(this.nodes.values());
    }

    // 获取在线节点数量
    getOnlineNodesCount() {
        return Array.from(this.nodes.values()).filter(node => node.status).length;
    }

    // 获取离线节点数量
    getOfflineNodesCount() {
        return Array.from(this.nodes.values()).filter(node => !node.status).length;
    }

    // 检查节点是否离线
    checkOfflineNodes() {
        const now = new Date();
        
        this.nodes.forEach((node, nodeId) => {
            if (node.lastUpdate && (now - node.lastUpdate) > this.offlineTimeout) {
                if (node.status) {
                    node.status = false;
                    this.updateNodeUI(nodeId);
                    console.log(`⚠️ 节点${nodeId}超时离线`);
                }
            }
        });
    }

    // 重置所有节点
    resetAllNodes() {
        this.nodes.forEach((node, nodeId) => {
            node.status = false;
            node.lastUpdate = null;
            node.currentData = {
                temperature: 0,
                humidity: 0,
                light: 0,
                smoke: 0
            };
            node.alertLevel = 'normal';
            
            this.updateNodeUI(nodeId);
        });
        
        console.log('🔄 所有节点已重置');
    }

    // 开始离线检查
    startOfflineCheck() {
        setInterval(() => {
            this.checkOfflineNodes();
        }, this.offlineTimeout);
        
        console.log('⏰ 节点离线检查已启动');
    }

    // 获取节点统计信息
    getStatistics() {
        const nodes = this.getAllNodes();
        
        return {
            total: nodes.length,
            online: this.getOnlineNodesCount(),
            offline: this.getOfflineNodesCount(),
            withAlerts: nodes.filter(node => node.alertLevel !== 'normal').length,
            lastUpdate: Math.max(...nodes.map(node => node.lastUpdate ? node.lastUpdate.getTime() : 0))
        };
    }
}

// 导出节点管理器
window.NodeManager = NodeManager;
