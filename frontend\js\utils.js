// 工具函数库 - 前端通用工具
class Utils {
    // 格式化时间
    static formatTime(date = new Date()) {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化相对时间
    static formatRelativeTime(date) {
        const now = new Date();
        const diff = now - new Date(date);
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (seconds < 60) return `${seconds}秒前`;
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return new Date(date).toLocaleDateString('zh-CN');
    }

    // 格式化传感器值
    static formatSensorValue(type, value) {
        const config = CONFIG.utils.getSensorConfig(type);
        if (!config) return value;
        
        const formattedValue = CONFIG.utils.formatSensorValue(type, value);
        return `${formattedValue}${config.unit}`;
    }

    // 验证传感器值范围
    static validateSensorValue(type, value) {
        return CONFIG.utils.validateSensorValue(type, value);
    }

    // 生成随机ID
    static generateId() {
        return Math.random().toString(36).substr(2, 9);
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 深拷贝
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => Utils.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    // 本地存储操作
    static storage = {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('存储数据失败:', error);
                return false;
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('读取数据失败:', error);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('删除数据失败:', error);
                return false;
            }
        },

        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('清空存储失败:', error);
                return false;
            }
        }
    };

    // DOM操作工具
    static dom = {
        // 查询元素
        $(selector) {
            return document.querySelector(selector);
        },

        // 查询所有元素
        $$(selector) {
            return document.querySelectorAll(selector);
        },

        // 创建元素
        create(tag, attributes = {}, content = '') {
            const element = document.createElement(tag);
            
            Object.keys(attributes).forEach(key => {
                if (key === 'className') {
                    element.className = attributes[key];
                } else if (key === 'innerHTML') {
                    element.innerHTML = attributes[key];
                } else {
                    element.setAttribute(key, attributes[key]);
                }
            });
            
            if (content) {
                element.textContent = content;
            }
            
            return element;
        },

        // 添加类名
        addClass(element, className) {
            if (element && className) {
                element.classList.add(className);
            }
        },

        // 移除类名
        removeClass(element, className) {
            if (element && className) {
                element.classList.remove(className);
            }
        },

        // 切换类名
        toggleClass(element, className) {
            if (element && className) {
                element.classList.toggle(className);
            }
        },

        // 检查是否有类名
        hasClass(element, className) {
            return element && className && element.classList.contains(className);
        }
    };

    // 动画工具
    static animation = {
        // 淡入
        fadeIn(element, duration = 300) {
            element.style.opacity = '0';
            element.style.display = 'block';
            
            const start = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                element.style.opacity = progress;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }
            
            requestAnimationFrame(animate);
        },

        // 淡出
        fadeOut(element, duration = 300) {
            const start = performance.now();
            const startOpacity = parseFloat(getComputedStyle(element).opacity);
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                element.style.opacity = startOpacity * (1 - progress);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    element.style.display = 'none';
                }
            }
            
            requestAnimationFrame(animate);
        },

        // 滑入
        slideIn(element, direction = 'down', duration = 300) {
            const transforms = {
                up: 'translateY(-20px)',
                down: 'translateY(20px)',
                left: 'translateX(-20px)',
                right: 'translateX(20px)'
            };
            
            element.style.transform = transforms[direction];
            element.style.opacity = '0';
            element.style.display = 'block';
            
            const start = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                const easeOut = 1 - Math.pow(1 - progress, 3);
                element.style.transform = `${transforms[direction].replace(/[-\d.]+/, (match) => {
                    return parseFloat(match) * (1 - easeOut);
                })}`;
                element.style.opacity = progress;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    element.style.transform = '';
                }
            }
            
            requestAnimationFrame(animate);
        }
    };

    // 网络请求工具
    static http = {
        // GET请求
        async get(url) {
            try {
                const response = await fetch(url);
                return await response.json();
            } catch (error) {
                console.error('GET请求失败:', error);
                throw error;
            }
        },

        // POST请求
        async post(url, data) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                console.error('POST请求失败:', error);
                throw error;
            }
        },

        // PUT请求
        async put(url, data) {
            try {
                const response = await fetch(url, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                console.error('PUT请求失败:', error);
                throw error;
            }
        },

        // DELETE请求
        async delete(url) {
            try {
                const response = await fetch(url, {
                    method: 'DELETE'
                });
                return await response.json();
            } catch (error) {
                console.error('DELETE请求失败:', error);
                throw error;
            }
        }
    };

    // 主题管理
    static theme = {
        // 获取当前主题
        get() {
            return Utils.storage.get('theme', 'light');
        },

        // 设置主题
        set(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            Utils.storage.set('theme', theme);
            
            // 更新主题切换器图标
            const themeIcon = Utils.dom.$('.theme-icon');
            if (themeIcon) {
                themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
            }
        },

        // 切换主题
        toggle() {
            const currentTheme = Utils.theme.get();
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            Utils.theme.set(newTheme);
            return newTheme;
        },

        // 初始化主题
        init() {
            const savedTheme = Utils.theme.get();
            Utils.theme.set(savedTheme);
        }
    };
}

// 导出工具类
window.Utils = Utils;
