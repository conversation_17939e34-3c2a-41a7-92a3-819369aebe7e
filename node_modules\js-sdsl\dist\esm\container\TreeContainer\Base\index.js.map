{"version": 3, "sources": ["container/TreeContainer/Base/index.js", "../../src/container/TreeContainer/Base/index.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__read", "o", "n", "m", "Symbol", "iterator", "i", "r", "ar", "e", "next", "done", "push", "value", "error", "__values", "s", "length", "TreeNode", "TreeNodeEnableIndex", "Container", "throwIteratorAccessError", "TreeC<PERSON>r", "_super", "cmp", "enableIndex", "x", "y", "_this", "_root", "undefined", "_cmp", "_TreeNodeClass", "_set", "key", "hint", "curNode", "_preSet", "_parent", "_header", "_subTreeSize", "nodeList", "_insertNodeSelfBalance", "_a", "parentNode", "grandParent", "curNode_1", "_recount", "_length", "_eraseNode", "_preEraseNode", "_lowerBound", "resNode", "cmpResult", "_key", "_right", "_left", "_upperBound", "_reverseLowerBound", "_reverseUpperBound", "_eraseNodeSelfBalance", "_color", "brother", "_rotateLeft", "_rotateRight", "_b", "clear", "swapNode", "_value", "_inOrderTraversal", "callback", "ifReturn", "uncle", "GP", "minNode", "compareToMin", "maxNode", "compareToMax", "iterNode", "_node", "iterCmpRes", "preNode", "_pre", "preCmpRes", "_findElementNode", "updateKeyByIterator", "iter", "node", "_next", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "eraseElementByPos", "pos", "RangeError", "index", "self", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eraseElementByIterator", "hasNoRight", "isNormal", "iteratorType", "for<PERSON>ach", "e_1", "_c", "element", "e_1_1", "return", "getElementByPos", "e_2", "res", "e_2_1", "getHeight", "traversal", "Math", "max"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,SAAUjB,QAAQA,KAAKiB,KAAW,SAAUC,GAAGC;IAC/C,IAAIC,WAAWC,WAAW,cAAcH,EAAEG,OAAOC;IACjD,KAAKF,GAAG,OAAOF;IACf,IAAIK,IAAIH,EAAET,KAAKO,IAAIM,GAAGC,IAAK,IAAIC;IAC/B;QACI,QAAQP,WAAW,KAAKA,MAAM,QAAQK,IAAID,EAAEI,QAAQC,MAAMH,EAAGI,KAAKL,EAAEM;AAQxE,MANA,OAAOC;QAASL,IAAI;YAAEK,OAAOA;;AAAS,MAAC;QAEnC;YACI,IAAIP,MAAMA,EAAEI,SAASR,IAAIG,EAAE,YAAYH,EAAET,KAAKY;AAElB,UAD/B;YACS,IAAIG,GAAG,MAAMA,EAAEK;AAAO;AACpC;IACA,OAAON;AACX;;AACA,IAAIO,WAAYhC,QAAQA,KAAKgC,KAAa,SAASd;IAC/C,IAAIe,WAAWZ,WAAW,cAAcA,OAAOC,UAAUF,IAAIa,KAAKf,EAAEe,IAAIV,IAAI;IAC5E,IAAIH,GAAG,OAAOA,EAAET,KAAKO;IACrB,IAAIA,YAAYA,EAAEgB,WAAW,UAAU,OAAO;QAC1CP,MAAM;YACF,IAAIT,KAAKK,KAAKL,EAAEgB,QAAQhB,SAAS;YACjC,OAAO;gBAAEY,OAAOZ,KAAKA,EAAEK;gBAAMK,OAAOV;;AACxC;;IAEJ,MAAM,IAAIN,UAAUqB,IAAI,4BAA4B;AACxD;;SCxCSE,UAAyBC,2BAA2B;;SACpDC,iBAAyB;;SAEzBC,gCAA0B;;AAEnC,IAAAC,gBAAA,SAAAC;IAA2CzC,UAAAwC,eAAAC;IA4BzC,SAAAD,cACEE,GAMAC;QANA,IAAAD,WAAA,GAAA;YAAAA,IAAA,SACUE,GAAMC;gBACd,IAAID,IAAIC,GAAG,QAAQ;gBACnB,IAAID,IAAIC,GAAG,OAAO;gBAClB,OAAO;ADmBL;AClBH;QACD,IAAAF,WAAA,GAAA;YAAAA,IAAA;AAAmB;QAPrB,IAAAG,IASEL,EAAA7B,KAAAX,SAAOA;QAjCC6C,EAAAC,IAAoCC;QAkC5CF,EAAKG,IAAOP;QACZ,IAAIC,GAAa;YACfG,EAAKI,KAAiBb;YACtBS,EAAKK,IAAO,SAAUC,GAAKrB,GAAOsB;gBAChC,IAAMC,IAAUrD,KAAKsD,GAAQH,GAAKrB,GAAOsB;gBACzC,IAAIC,GAAS;oBACX,IAAI7C,IAAI6C,EAAQE;oBAChB,OAAO/C,MAAMR,KAAKwD,GAAS;wBACzBhD,EAAEiD,MAAgB;wBAClBjD,IAAIA,EAAE+C;ADsBE;oBCpBV,IAAMG,IAAW1D,KAAK2D,GAAuBN;oBAC7C,IAAIK,GAAU;wBACN,IAAAE,IAIFF,GAHFG,IAAUD,EAAAC,YACVC,IAAWF,EAAAE,aACXC,IAAOH,EAAAP;wBAETQ,EAAWG;wBACXF,EAAYE;wBACZD,EAAQC;ADkBA;AACJ;gBChBR,OAAOhE,KAAKiE;ADkBR;YChBNpB,EAAKqB,IAAa,SAAUb;gBAC1B,IAAI7C,IAAIR,KAAKmE,GAAcd;gBAC3B,OAAO7C,MAAMR,KAAKwD,GAAS;oBACzBhD,EAAEiD,MAAgB;oBAClBjD,IAAIA,EAAE+C;ADkBA;AACJ;AACJ,eCjBG;YACLV,EAAKI,KAAiBd;YACtBU,EAAKK,IAAO,SAAUC,GAAKrB,GAAOsB;gBAChC,IAAMC,IAAUrD,KAAKsD,GAAQH,GAAKrB,GAAOsB;gBACzC,IAAIC,GAASrD,KAAK2D,GAAuBN;gBACzC,OAAOrD,KAAKiE;ADoBR;YClBNpB,EAAKqB,IAAarB,EAAKsB;ADoBrB;QClBJtB,EAAKW,IAAU,IAAIX,EAAKI;QDoBpB,OAAOJ;AACX;IChBQN,cAAA9B,UAAA2D,IAAV,SAAsBf,GAAqCF;QACzD,IAAIkB,IAAUrE,KAAKwD;QACnB,OAAOH,GAAS;YACd,IAAMiB,IAAYtE,KAAKgD,EAAKK,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBjB,IAAUA,EAAQmB;ADqBd,mBCpBC,IAAIF,IAAY,GAAG;gBACxBD,IAAUhB;gBACVA,IAAUA,EAAQoB;ADsBd,mBCrBC,OAAOpB;ADwBZ;QCtBJ,OAAOgB;ADwBP;ICnBQ9B,cAAA9B,UAAAiE,IAAV,SAAsBrB,GAAqCF;QACzD,IAAIkB,IAAUrE,KAAKwD;QACnB,OAAOH,GAAS;YACd,IAAMiB,IAAYtE,KAAKgD,EAAKK,EAAQkB,GAAOpB;YAC3C,IAAImB,KAAa,GAAG;gBAClBjB,IAAUA,EAAQmB;ADwBd,mBCvBC;gBACLH,IAAUhB;gBACVA,IAAUA,EAAQoB;ADyBd;AACJ;QCvBJ,OAAOJ;ADyBP;ICpBQ9B,cAAA9B,UAAAkE,IAAV,SAA6BtB,GAAqCF;QAChE,IAAIkB,IAAUrE,KAAKwD;QACnB,OAAOH,GAAS;YACd,IAAMiB,IAAYtE,KAAKgD,EAAKK,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBD,IAAUhB;gBACVA,IAAUA,EAAQmB;ADyBd,mBCxBC,IAAIF,IAAY,GAAG;gBACxBjB,IAAUA,EAAQoB;AD0Bd,mBCzBC,OAAOpB;AD4BZ;QC1BJ,OAAOgB;AD4BP;ICvBQ9B,cAAA9B,UAAAmE,IAAV,SAA6BvB,GAAqCF;QAChE,IAAIkB,IAAUrE,KAAKwD;QACnB,OAAOH,GAAS;YACd,IAAMiB,IAAYtE,KAAKgD,EAAKK,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBD,IAAUhB;gBACVA,IAAUA,EAAQmB;AD4Bd,mBC3BC;gBACLnB,IAAUA,EAAQoB;AD6Bd;AACJ;QC3BJ,OAAOJ;AD6BP;ICxBQ9B,cAAA9B,UAAAoE,KAAV,SAAgCxB;QAC9B,OAAO,MAAM;YACX,IAAMQ,IAAaR,EAAQE;YAC3B,IAAIM,MAAe7D,KAAKwD,GAAS;YACjC,IAAIH,EAAQyB,OAAM,GAAwB;gBACxCzB,EAAQyB,KAAM;gBACd;AD8BI;YC5BN,IAAIzB,MAAYQ,EAAWY,GAAO;gBAChC,IAAMM,IAAUlB,EAAWW;gBAC3B,IAAIO,EAAQD,OAAM,GAAwB;oBACxCC,EAAQD,KAAM;oBACdjB,EAAWiB,KAAM;oBACjB,IAAIjB,MAAe7D,KAAK8C,GAAO;wBAC7B9C,KAAK8C,IAAQe,EAAWmB;AD8BhB,2BC7BHnB,EAAWmB;ADgCZ,uBC/BD;oBACL,IAAID,EAAQP,KAAUO,EAAQP,EAAOM,OAAM,GAAwB;wBACjEC,EAAQD,KAASjB,EAAWiB;wBAC5BjB,EAAWiB,KAAM;wBACjBC,EAAQP,EAAOM,KAAM;wBACrB,IAAIjB,MAAe7D,KAAK8C,GAAO;4BAC7B9C,KAAK8C,IAAQe,EAAWmB;ADiCd,+BChCLnB,EAAWmB;wBAClB;ADmCQ,2BClCH,IAAID,EAAQN,KAASM,EAAQN,EAAMK,OAAM,GAAwB;wBACtEC,EAAQD,KAAM;wBACdC,EAAQN,EAAMK,KAAM;wBACpBC,EAAQE;ADoCA,2BCnCH;wBACLF,EAAQD,KAAM;wBACdzB,IAAUQ;ADqCF;AACJ;AACJ,mBCpCC;gBACL,IAAMkB,IAAUlB,EAAWY;gBAC3B,IAAIM,EAAQD,OAAM,GAAwB;oBACxCC,EAAQD,KAAM;oBACdjB,EAAWiB,KAAM;oBACjB,IAAIjB,MAAe7D,KAAK8C,GAAO;wBAC7B9C,KAAK8C,IAAQe,EAAWoB;ADsChB,2BCrCHpB,EAAWoB;ADwCZ,uBCvCD;oBACL,IAAIF,EAAQN,KAASM,EAAQN,EAAMK,OAAM,GAAwB;wBAC/DC,EAAQD,KAASjB,EAAWiB;wBAC5BjB,EAAWiB,KAAM;wBACjBC,EAAQN,EAAMK,KAAM;wBACpB,IAAIjB,MAAe7D,KAAK8C,GAAO;4BAC7B9C,KAAK8C,IAAQe,EAAWoB;ADyCd,+BCxCLpB,EAAWoB;wBAClB;AD2CQ,2BC1CH,IAAIF,EAAQP,KAAUO,EAAQP,EAAOM,OAAM,GAAwB;wBACxEC,EAAQD,KAAM;wBACdC,EAAQP,EAAOM,KAAM;wBACrBC,EAAQC;AD4CA,2BC3CH;wBACLD,EAAQD,KAAM;wBACdzB,IAAUQ;AD6CF;AACJ;AACJ;AACJ;AACJ;ICxCQtB,cAAA9B,UAAA0D,KAAV,SAAwBd;QD6ClB,IAAIO,GAAIsB;QC5CZ,IAAIlF,KAAKiE,MAAY,GAAG;YACtBjE,KAAKmF;YACL,OAAOnF,KAAKwD;AD8CV;QC5CJ,IAAI4B,IAAW/B;QACf,OAAO+B,EAASX,KAASW,EAASZ,GAAQ;YACxC,IAAIY,EAASZ,GAAQ;gBACnBY,IAAWA,EAASZ;gBACpB,OAAOY,EAASX,GAAOW,IAAWA,EAASX;AD+CvC,mBC9CC;gBACLW,IAAWA,EAASX;ADgDhB;YC9CNb,IAAA3C,OAAgC,EAACmE,EAASb,GAAMlB,EAAQkB,KAAK,IAA5DlB,EAAQkB,IAAIX,EAAA,IAAEwB,EAASb,IAAIX,EAAA;YAC5BsB,IAAAjE,OAAoC,EAACmE,EAASC,GAAQhC,EAAQgC,KAAO,IAApEhC,EAAQgC,IAAMH,EAAA,IAAEE,EAASC,IAAMH,EAAA;YAChC7B,IAAU+B;ADgDR;QC9CJ,IAAIpF,KAAKwD,EAAQiB,MAAUW,GAAU;YACnCpF,KAAKwD,EAAQiB,IAAQW,EAAS7B;ADgD5B,eC/CG,IAAIvD,KAAKwD,EAAQgB,MAAWY,GAAU;YAC3CpF,KAAKwD,EAAQgB,IAASY,EAAS7B;ADiD7B;QC/CJvD,KAAK6E,GAAsBO;QAC3B,IAAM7B,IAAU6B,EAAS7B;QACzB,IAAI6B,MAAa7B,EAAQkB,GAAO;YAC9BlB,EAAQkB,IAAQ1B;ADiDd,eChDGQ,EAAQiB,IAASzB;QACxB/C,KAAKiE,KAAW;QAChBjE,KAAK8C,EAAOgC,KAAM;QAClB,OAAOvB;ADmDP;IC9CQhB,cAAA9B,UAAA6E,KAAV,SACEjC,GACAkC;QAEA,IAAIlC,MAAYN,WAAW,OAAO;QAClC,IAAMyC,IAAWxF,KAAKsF,GAAkBjC,EAAQoB,GAAOc;QACvD,IAAIC,GAAU,OAAO;QACrB,IAAID,EAASlC,IAAU,OAAO;QAC9B,OAAOrD,KAAKsF,GAAkBjC,EAAQmB,GAAQe;ADmD9C;IC9CQhD,cAAA9B,UAAAkD,KAAV,SAAiCN;QAC/B,OAAO,MAAM;YACX,IAAMQ,IAAaR,EAAQE;YAC3B,IAAIM,EAAWiB,OAAM,GAA0B;YAC/C,IAAMhB,IAAcD,EAAWN;YAC/B,IAAIM,MAAeC,EAAYW,GAAO;gBACpC,IAAMgB,IAAQ3B,EAAYU;gBAC1B,IAAIiB,KAASA,EAAMX,OAAM,GAAwB;oBAC/CW,EAAMX,KAASjB,EAAWiB,KAAM;oBAChC,IAAIhB,MAAgB9D,KAAK8C,GAAO;oBAChCgB,EAAYgB,KAAM;oBAClBzB,IAAUS;oBACV;ADqDM,uBCpDD,IAAIT,MAAYQ,EAAWW,GAAQ;oBACxCnB,EAAQyB,KAAM;oBACd,IAAIzB,EAAQoB,GAAOpB,EAAQoB,EAAMlB,KAAUM;oBAC3C,IAAIR,EAAQmB,GAAQnB,EAAQmB,EAAOjB,KAAUO;oBAC7CD,EAAWW,IAASnB,EAAQoB;oBAC5BX,EAAYW,IAAQpB,EAAQmB;oBAC5BnB,EAAQoB,IAAQZ;oBAChBR,EAAQmB,IAASV;oBACjB,IAAIA,MAAgB9D,KAAK8C,GAAO;wBAC9B9C,KAAK8C,IAAQO;wBACbrD,KAAKwD,EAAQD,KAAUF;ADwDf,2BCvDH;wBACL,IAAMqC,IAAK5B,EAAYP;wBACvB,IAAImC,EAAGjB,MAAUX,GAAa;4BAC5B4B,EAAGjB,IAAQpB;ADyDD,+BCxDLqC,EAAGlB,IAASnB;AD2DX;oBCzDVA,EAAQE,KAAUO,EAAYP;oBAC9BM,EAAWN,KAAUF;oBACrBS,EAAYP,KAAUF;oBACtBS,EAAYgB,KAAM;oBAClB,OAAO;wBAAEjB,YAAUA;wBAAEC,aAAWA;wBAAET,SAAOA;;AD2DnC,uBC1DD;oBACLQ,EAAWiB,KAAM;oBACjB,IAAIhB,MAAgB9D,KAAK8C,GAAO;wBAC9B9C,KAAK8C,IAAQgB,EAAYmB;AD4DjB,2BC3DHnB,EAAYmB;oBACnBnB,EAAYgB,KAAM;AD8DZ;AACJ,mBC7DC;gBACL,IAAMW,IAAQ3B,EAAYW;gBAC1B,IAAIgB,KAASA,EAAMX,OAAM,GAAwB;oBAC/CW,EAAMX,KAASjB,EAAWiB,KAAM;oBAChC,IAAIhB,MAAgB9D,KAAK8C,GAAO;oBAChCgB,EAAYgB,KAAM;oBAClBzB,IAAUS;oBACV;ADgEM,uBC/DD,IAAIT,MAAYQ,EAAWY,GAAO;oBACvCpB,EAAQyB,KAAM;oBACd,IAAIzB,EAAQoB,GAAOpB,EAAQoB,EAAMlB,KAAUO;oBAC3C,IAAIT,EAAQmB,GAAQnB,EAAQmB,EAAOjB,KAAUM;oBAC7CC,EAAYU,IAASnB,EAAQoB;oBAC7BZ,EAAWY,IAAQpB,EAAQmB;oBAC3BnB,EAAQoB,IAAQX;oBAChBT,EAAQmB,IAASX;oBACjB,IAAIC,MAAgB9D,KAAK8C,GAAO;wBAC9B9C,KAAK8C,IAAQO;wBACbrD,KAAKwD,EAAQD,KAAUF;ADmEf,2BClEH;wBACL,IAAMqC,IAAK5B,EAAYP;wBACvB,IAAImC,EAAGjB,MAAUX,GAAa;4BAC5B4B,EAAGjB,IAAQpB;ADoED,+BCnELqC,EAAGlB,IAASnB;ADsEX;oBCpEVA,EAAQE,KAAUO,EAAYP;oBAC9BM,EAAWN,KAAUF;oBACrBS,EAAYP,KAAUF;oBACtBS,EAAYgB,KAAM;oBAClB,OAAO;wBAAEjB,YAAUA;wBAAEC,aAAWA;wBAAET,SAAOA;;ADsEnC,uBCrED;oBACLQ,EAAWiB,KAAM;oBACjB,IAAIhB,MAAgB9D,KAAK8C,GAAO;wBAC9B9C,KAAK8C,IAAQgB,EAAYkB;ADuEjB,2BCtEHlB,EAAYkB;oBACnBlB,EAAYgB,KAAM;ADyEZ;AACJ;YCvEN;ADyEE;AACJ;ICpEQvC,cAAA9B,UAAA6C,KAAV,SAAkBH,GAAQrB,GAAWsB;QACnC,IAAIpD,KAAK8C,MAAUC,WAAW;YAC5B/C,KAAKiE,KAAW;YAChBjE,KAAK8C,IAAQ,IAAI9C,KAAKiD,GAAeE,GAAKrB;YAC1C9B,KAAK8C,EAAMgC,KAAM;YACjB9E,KAAK8C,EAAMS,KAAUvD,KAAKwD;YAC1BxD,KAAKwD,EAAQD,KAAUvD,KAAK8C;YAC5B9C,KAAKwD,EAAQiB,IAAQzE,KAAK8C;YAC1B9C,KAAKwD,EAAQgB,IAASxE,KAAK8C;YAC3B;ADyEE;QCvEJ,IAAIO;QACJ,IAAMsC,IAAU3F,KAAKwD,EAAQiB;QAC7B,IAAMmB,IAAe5F,KAAKgD,EAAK2C,EAAQpB,GAAOpB;QAC9C,IAAIyC,MAAiB,GAAG;YACtBD,EAAQN,IAASvD;YACjB;ADyEE,eCxEG,IAAI8D,IAAe,GAAG;YAC3BD,EAAQlB,IAAQ,IAAIzE,KAAKiD,GAAeE,GAAKrB;YAC7C6D,EAAQlB,EAAMlB,KAAUoC;YACxBtC,IAAUsC,EAAQlB;YAClBzE,KAAKwD,EAAQiB,IAAQpB;AD0EnB,eCzEG;YACL,IAAMwC,IAAU7F,KAAKwD,EAAQgB;YAC7B,IAAMsB,IAAe9F,KAAKgD,EAAK6C,EAAQtB,GAAOpB;YAC9C,IAAI2C,MAAiB,GAAG;gBACtBD,EAAQR,IAASvD;gBACjB;AD2EI,mBC1EC,IAAIgE,IAAe,GAAG;gBAC3BD,EAAQrB,IAAS,IAAIxE,KAAKiD,GAAeE,GAAKrB;gBAC9C+D,EAAQrB,EAAOjB,KAAUsC;gBACzBxC,IAAUwC,EAAQrB;gBAClBxE,KAAKwD,EAAQgB,IAASnB;AD4ElB,mBC3EC;gBACL,IAAID,MAASL,WAAW;oBACtB,IAAMgD,IAAW3C,EAAK4C;oBACtB,IAAID,MAAa/F,KAAKwD,GAAS;wBAC7B,IAAMyC,IAAajG,KAAKgD,EAAK+C,EAASxB,GAAOpB;wBAC7C,IAAI8C,MAAe,GAAG;4BACpBF,EAASV,IAASvD;4BAClB;AD6EU,+BC5EsB,IAAImE,IAAa,GAAG;4BACpD,IAAMC,IAAUH,EAASI;4BACzB,IAAMC,IAAYpG,KAAKgD,EAAKkD,EAAQ3B,GAAOpB;4BAC3C,IAAIiD,MAAc,GAAG;gCACnBF,EAAQb,IAASvD;gCACjB;AD8EY,mCC7EP,IAAIsE,IAAY,GAAG;gCACxB/C,IAAU,IAAIrD,KAAKiD,GAAeE,GAAKrB;gCACvC,IAAIoE,EAAQ1B,MAAWzB,WAAW;oCAChCmD,EAAQ1B,IAASnB;oCACjBA,EAAQE,KAAU2C;AD+EJ,uCC9ET;oCACLH,EAAStB,IAAQpB;oCACjBA,EAAQE,KAAUwC;ADgFJ;AACJ;AACJ;AACJ;AACJ;gBC9ER,IAAI1C,MAAYN,WAAW;oBACzBM,IAAUrD,KAAK8C;oBACf,OAAO,MAAM;wBACX,IAAMwB,IAAYtE,KAAKgD,EAAKK,EAAQkB,GAAOpB;wBAC3C,IAAImB,IAAY,GAAG;4BACjB,IAAIjB,EAAQoB,MAAU1B,WAAW;gCAC/BM,EAAQoB,IAAQ,IAAIzE,KAAKiD,GAAeE,GAAKrB;gCAC7CuB,EAAQoB,EAAMlB,KAAUF;gCACxBA,IAAUA,EAAQoB;gCAClB;ADgFY;4BC9EdpB,IAAUA,EAAQoB;ADgFR,+BC/EL,IAAIH,IAAY,GAAG;4BACxB,IAAIjB,EAAQmB,MAAWzB,WAAW;gCAChCM,EAAQmB,IAAS,IAAIxE,KAAKiD,GAAeE,GAAKrB;gCAC9CuB,EAAQmB,EAAOjB,KAAUF;gCACzBA,IAAUA,EAAQmB;gCAClB;ADiFY;4BC/EdnB,IAAUA,EAAQmB;ADiFR,+BChFL;4BACLnB,EAAQgC,IAASvD;4BACjB;ADkFU;AACJ;AACJ;AACJ;AACJ;QChFJ9B,KAAKiE,KAAW;QAChB,OAAOZ;ADkFP;IC7EQd,cAAA9B,UAAA4F,IAAV,SAA2BhD,GAAqCF;QAC9D,OAAOE,GAAS;YACd,IAAMiB,IAAYtE,KAAKgD,EAAKK,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBjB,IAAUA,EAAQmB;ADkFd,mBCjFC,IAAIF,IAAY,GAAG;gBACxBjB,IAAUA,EAAQoB;ADmFd,mBClFC,OAAOpB;ADqFZ;QCnFJ,OAAOA,KAAWrD,KAAKwD;ADqFvB;ICnFFjB,cAAA9B,UAAA0E,QAAA;QACEnF,KAAKiE,IAAU;QACfjE,KAAK8C,IAAQC;QACb/C,KAAKwD,EAAQD,KAAUR;QACvB/C,KAAKwD,EAAQiB,IAAQzE,KAAKwD,EAAQgB,IAASzB;ADqF3C;ICzEFR,cAAA9B,UAAA6F,sBAAA,SAAoBC,GAA0BpD;QAC5C,IAAMqD,IAAOD,EAAKP;QAClB,IAAIQ,MAASxG,KAAKwD,GAAS;YACzBlB;ADqFE;QCnFJ,IAAItC,KAAKiE,MAAY,GAAG;YACtBuC,EAAKjC,IAAOpB;YACZ,OAAO;ADqFL;QCnFJ,IAAIqD,MAASxG,KAAKwD,EAAQiB,GAAO;YAC/B,IAAIzE,KAAKgD,EAAKwD,EAAKC,IAAQlC,GAAOpB,KAAO,GAAG;gBAC1CqD,EAAKjC,IAAOpB;gBACZ,OAAO;ADqFH;YCnFN,OAAO;ADqFL;QCnFJ,IAAIqD,MAASxG,KAAKwD,EAAQgB,GAAQ;YAChC,IAAIxE,KAAKgD,EAAKwD,EAAKL,IAAO5B,GAAOpB,KAAO,GAAG;gBACzCqD,EAAKjC,IAAOpB;gBACZ,OAAO;ADqFH;YCnFN,OAAO;ADqFL;QCnFJ,IAAMuD,IAASF,EAAKL,IAAO5B;QAC3B,IAAIvE,KAAKgD,EAAK0D,GAAQvD,MAAQ,GAAG,OAAO;QACxC,IAAMwD,IAAUH,EAAKC,IAAQlC;QAC7B,IAAIvE,KAAKgD,EAAK2D,GAASxD,MAAQ,GAAG,OAAO;QACzCqD,EAAKjC,IAAOpB;QACZ,OAAO;ADuFP;ICrFFZ,cAAA9B,UAAAmG,oBAAA,SAAkBC;QDuFZ,ICtFsBA,IAAG,KAAHA,IAAQ7G,KAAKiE,IAAO,GAnfd;YAAE,MAAU,IAAI6C;AD2kB5C;QCvFJ,IAAIC,IAAQ;QACZ,IAAMC,IAAOhH;QACbA,KAAKsF,GACHtF,KAAK8C,IACL,SAAUO;YACR,IAAIwD,MAAQE,GAAO;gBACjBC,EAAK9C,EAAWb;gBAChB,OAAO;ADuFL;YCrFJ0D,KAAS;YACT,OAAO;ADuFP;QCrFJ,OAAO/G,KAAKiE;ADuFZ;IChFF1B,cAAA9B,UAAAwG,oBAAA,SAAkB9D;QAChB,IAAInD,KAAKiE,MAAY,GAAG,OAAO;QAC/B,IAAMZ,IAAUrD,KAAKqG,EAAiBrG,KAAK8C,GAAOK;QAClD,IAAIE,MAAYrD,KAAKwD,GAAS,OAAO;QACrCxD,KAAKkE,EAAWb;QAChB,OAAO;ADyFP;ICvFFd,cAAA9B,UAAAyG,yBAAA,SAAuBX;QACrB,IAAMC,IAAOD,EAAKP;QAClB,IAAIQ,MAASxG,KAAKwD,GAAS;YACzBlB;ADyFE;QCvFJ,IAAM6E,IAAaX,EAAKhC,MAAWzB;QACnC,IAAMqE,IAAWb,EAAKc,iBAAY;QAElC,IAAID,GAAU;YAEZ,IAAID,GAAYZ,EAAK5E;AD0FnB,eCzFG;YAGL,KAAKwF,KAAcX,EAAK/B,MAAU1B,WAAWwD,EAAK5E;AD4FhD;QC1FJ3B,KAAKkE,EAAWsC;QAChB,OAAOD;AD4FP;IC1FFhE,cAAA9B,UAAA6G,UAAA,SAAQ/B;QD4FF,IAAIgC,GAAK3D;QC3Fb,IAAImD,IAAQ;QD6FR;YC5FJ,KAAsB,IAAA7B,IAAAlD,SAAAhC,OAAIwH,IAAAtC,EAAAvD,SAAA6F,EAAA5F,MAAA4F,IAAAtC,EAAAvD,QAAA;gBAArB,IAAM8F,IAAOD,EAAA1F;gBAAUyD,EAASkC,GAASV,KAAS/G;ADgG/C;AAQJ,UANA,OAAO0H;YAASH,IAAM;gBAAExF,OAAO2F;;AAAS,UAAC;YAErC;gBACI,IAAIF,MAAOA,EAAG5F,SAASgC,IAAKsB,EAAGyC,SAAS/D,EAAGjD,KAAKuE;AAEhB,cADnC;gBACS,IAAIqC,GAAK,MAAMA,EAAIxF;AAAO;AACxC;AACJ;ICvGFQ,cAAA9B,UAAAmH,kBAAA,SAAgBf;QDyGV,IAAIgB,GAAKjE;QACT,ICzGsBiD,IAAG,KAAHA,IAAQ7G,KAAKiE,IAAO,GAtiBd;YAAE,MAAU,IAAI6C;ADipB5C;QC1GJ,IAAIgB;QACJ,IAAIf,IAAQ;QD4GR;YC3GJ,KAAsB,IAAA7B,IAAAlD,SAAAhC,OAAIwH,IAAAtC,EAAAvD,SAAA6F,EAAA5F,MAAA4F,IAAAtC,EAAAvD,QAAE;gBAAvB,IAAM8F,IAAOD,EAAA1F;gBAChB,IAAIiF,MAAUF,GAAK;oBACjBiB,IAAML;oBACN;AD8GQ;gBC5GVV,KAAS;AD8GH;AAQJ,UANA,OAAOgB;YAASF,IAAM;gBAAE9F,OAAOgG;;AAAS,UAAC;YAErC;gBACI,IAAIP,MAAOA,EAAG5F,SAASgC,IAAKsB,EAAGyC,SAAS/D,EAAGjD,KAAKuE;AAEhB,cADnC;gBACS,IAAI2C,GAAK,MAAMA,EAAI9F;AAAO;AACxC;QCpHJ,OAAmB+F;ADsHnB;IChHFvF,cAAA9B,UAAAuH,YAAA;QACE,IAAIhI,KAAKiE,MAAY,GAAG,OAAO;QAC/B,IAAMgE,YACJ,SAAU5E;YACR,KAAKA,GAAS,OAAO;YACrB,OAAO6E,KAAKC,IAAIF,UAAU5E,EAAQoB,IAAQwD,UAAU5E,EAAQmB,MAAW;ADuHvE;QCrHJ,OAAOyD,UAAUjI,KAAK8C;ADuHtB;IC3FJ,OAAAP;AAAA,CAzlBA,CAA2CF;;eA2lB5BE", "file": "index.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { TreeNode, TreeNodeEnableIndex } from './TreeNode';\nimport { Container } from \"../../ContainerBase\";\nimport $checkWithinAccessParams from \"../../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nvar TreeContainer = /** @class */ (function (_super) {\n    __extends(TreeContainer, _super);\n    /**\n     * @internal\n     */\n    function TreeContainer(cmp, enableIndex) {\n        if (cmp === void 0) { cmp = function (x, y) {\n            if (x < y)\n                return -1;\n            if (x > y)\n                return 1;\n            return 0;\n        }; }\n        if (enableIndex === void 0) { enableIndex = false; }\n        var _this = _super.call(this) || this;\n        /**\n         * @internal\n         */\n        _this._root = undefined;\n        _this._cmp = cmp;\n        if (enableIndex) {\n            _this._TreeNodeClass = TreeNodeEnableIndex;\n            _this._set = function (key, value, hint) {\n                var curNode = this._preSet(key, value, hint);\n                if (curNode) {\n                    var p = curNode._parent;\n                    while (p !== this._header) {\n                        p._subTreeSize += 1;\n                        p = p._parent;\n                    }\n                    var nodeList = this._insertNodeSelfBalance(curNode);\n                    if (nodeList) {\n                        var _a = nodeList, parentNode = _a.parentNode, grandParent = _a.grandParent, curNode_1 = _a.curNode;\n                        parentNode._recount();\n                        grandParent._recount();\n                        curNode_1._recount();\n                    }\n                }\n                return this._length;\n            };\n            _this._eraseNode = function (curNode) {\n                var p = this._preEraseNode(curNode);\n                while (p !== this._header) {\n                    p._subTreeSize -= 1;\n                    p = p._parent;\n                }\n            };\n        }\n        else {\n            _this._TreeNodeClass = TreeNode;\n            _this._set = function (key, value, hint) {\n                var curNode = this._preSet(key, value, hint);\n                if (curNode)\n                    this._insertNodeSelfBalance(curNode);\n                return this._length;\n            };\n            _this._eraseNode = _this._preEraseNode;\n        }\n        _this._header = new _this._TreeNodeClass();\n        return _this;\n    }\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._lowerBound = function (curNode, key) {\n        var resNode = this._header;\n        while (curNode) {\n            var cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                curNode = curNode._right;\n            }\n            else if (cmpResult > 0) {\n                resNode = curNode;\n                curNode = curNode._left;\n            }\n            else\n                return curNode;\n        }\n        return resNode;\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._upperBound = function (curNode, key) {\n        var resNode = this._header;\n        while (curNode) {\n            var cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult <= 0) {\n                curNode = curNode._right;\n            }\n            else {\n                resNode = curNode;\n                curNode = curNode._left;\n            }\n        }\n        return resNode;\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._reverseLowerBound = function (curNode, key) {\n        var resNode = this._header;\n        while (curNode) {\n            var cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                resNode = curNode;\n                curNode = curNode._right;\n            }\n            else if (cmpResult > 0) {\n                curNode = curNode._left;\n            }\n            else\n                return curNode;\n        }\n        return resNode;\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._reverseUpperBound = function (curNode, key) {\n        var resNode = this._header;\n        while (curNode) {\n            var cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                resNode = curNode;\n                curNode = curNode._right;\n            }\n            else {\n                curNode = curNode._left;\n            }\n        }\n        return resNode;\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._eraseNodeSelfBalance = function (curNode) {\n        while (true) {\n            var parentNode = curNode._parent;\n            if (parentNode === this._header)\n                return;\n            if (curNode._color === 1 /* TreeNodeColor.RED */) {\n                curNode._color = 0 /* TreeNodeColor.BLACK */;\n                return;\n            }\n            if (curNode === parentNode._left) {\n                var brother = parentNode._right;\n                if (brother._color === 1 /* TreeNodeColor.RED */) {\n                    brother._color = 0 /* TreeNodeColor.BLACK */;\n                    parentNode._color = 1 /* TreeNodeColor.RED */;\n                    if (parentNode === this._root) {\n                        this._root = parentNode._rotateLeft();\n                    }\n                    else\n                        parentNode._rotateLeft();\n                }\n                else {\n                    if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = parentNode._color;\n                        parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._right._color = 0 /* TreeNodeColor.BLACK */;\n                        if (parentNode === this._root) {\n                            this._root = parentNode._rotateLeft();\n                        }\n                        else\n                            parentNode._rotateLeft();\n                        return;\n                    }\n                    else if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        brother._left._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._rotateRight();\n                    }\n                    else {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        curNode = parentNode;\n                    }\n                }\n            }\n            else {\n                var brother = parentNode._left;\n                if (brother._color === 1 /* TreeNodeColor.RED */) {\n                    brother._color = 0 /* TreeNodeColor.BLACK */;\n                    parentNode._color = 1 /* TreeNodeColor.RED */;\n                    if (parentNode === this._root) {\n                        this._root = parentNode._rotateRight();\n                    }\n                    else\n                        parentNode._rotateRight();\n                }\n                else {\n                    if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = parentNode._color;\n                        parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._left._color = 0 /* TreeNodeColor.BLACK */;\n                        if (parentNode === this._root) {\n                            this._root = parentNode._rotateRight();\n                        }\n                        else\n                            parentNode._rotateRight();\n                        return;\n                    }\n                    else if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        brother._right._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._rotateLeft();\n                    }\n                    else {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        curNode = parentNode;\n                    }\n                }\n            }\n        }\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._preEraseNode = function (curNode) {\n        var _a, _b;\n        if (this._length === 1) {\n            this.clear();\n            return this._header;\n        }\n        var swapNode = curNode;\n        while (swapNode._left || swapNode._right) {\n            if (swapNode._right) {\n                swapNode = swapNode._right;\n                while (swapNode._left)\n                    swapNode = swapNode._left;\n            }\n            else {\n                swapNode = swapNode._left;\n            }\n            _a = __read([swapNode._key, curNode._key], 2), curNode._key = _a[0], swapNode._key = _a[1];\n            _b = __read([swapNode._value, curNode._value], 2), curNode._value = _b[0], swapNode._value = _b[1];\n            curNode = swapNode;\n        }\n        if (this._header._left === swapNode) {\n            this._header._left = swapNode._parent;\n        }\n        else if (this._header._right === swapNode) {\n            this._header._right = swapNode._parent;\n        }\n        this._eraseNodeSelfBalance(swapNode);\n        var _parent = swapNode._parent;\n        if (swapNode === _parent._left) {\n            _parent._left = undefined;\n        }\n        else\n            _parent._right = undefined;\n        this._length -= 1;\n        this._root._color = 0 /* TreeNodeColor.BLACK */;\n        return _parent;\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._inOrderTraversal = function (curNode, callback) {\n        if (curNode === undefined)\n            return false;\n        var ifReturn = this._inOrderTraversal(curNode._left, callback);\n        if (ifReturn)\n            return true;\n        if (callback(curNode))\n            return true;\n        return this._inOrderTraversal(curNode._right, callback);\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._insertNodeSelfBalance = function (curNode) {\n        while (true) {\n            var parentNode = curNode._parent;\n            if (parentNode._color === 0 /* TreeNodeColor.BLACK */)\n                return;\n            var grandParent = parentNode._parent;\n            if (parentNode === grandParent._left) {\n                var uncle = grandParent._right;\n                if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n                    uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root)\n                        return;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    curNode = grandParent;\n                    continue;\n                }\n                else if (curNode === parentNode._right) {\n                    curNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (curNode._left)\n                        curNode._left._parent = parentNode;\n                    if (curNode._right)\n                        curNode._right._parent = grandParent;\n                    parentNode._right = curNode._left;\n                    grandParent._left = curNode._right;\n                    curNode._left = parentNode;\n                    curNode._right = grandParent;\n                    if (grandParent === this._root) {\n                        this._root = curNode;\n                        this._header._parent = curNode;\n                    }\n                    else {\n                        var GP = grandParent._parent;\n                        if (GP._left === grandParent) {\n                            GP._left = curNode;\n                        }\n                        else\n                            GP._right = curNode;\n                    }\n                    curNode._parent = grandParent._parent;\n                    parentNode._parent = curNode;\n                    grandParent._parent = curNode;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    return { parentNode: parentNode, grandParent: grandParent, curNode: curNode };\n                }\n                else {\n                    parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root) {\n                        this._root = grandParent._rotateRight();\n                    }\n                    else\n                        grandParent._rotateRight();\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                }\n            }\n            else {\n                var uncle = grandParent._left;\n                if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n                    uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root)\n                        return;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    curNode = grandParent;\n                    continue;\n                }\n                else if (curNode === parentNode._left) {\n                    curNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (curNode._left)\n                        curNode._left._parent = grandParent;\n                    if (curNode._right)\n                        curNode._right._parent = parentNode;\n                    grandParent._right = curNode._left;\n                    parentNode._left = curNode._right;\n                    curNode._left = grandParent;\n                    curNode._right = parentNode;\n                    if (grandParent === this._root) {\n                        this._root = curNode;\n                        this._header._parent = curNode;\n                    }\n                    else {\n                        var GP = grandParent._parent;\n                        if (GP._left === grandParent) {\n                            GP._left = curNode;\n                        }\n                        else\n                            GP._right = curNode;\n                    }\n                    curNode._parent = grandParent._parent;\n                    parentNode._parent = curNode;\n                    grandParent._parent = curNode;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    return { parentNode: parentNode, grandParent: grandParent, curNode: curNode };\n                }\n                else {\n                    parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root) {\n                        this._root = grandParent._rotateLeft();\n                    }\n                    else\n                        grandParent._rotateLeft();\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                }\n            }\n            return;\n        }\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._preSet = function (key, value, hint) {\n        if (this._root === undefined) {\n            this._length += 1;\n            this._root = new this._TreeNodeClass(key, value);\n            this._root._color = 0 /* TreeNodeColor.BLACK */;\n            this._root._parent = this._header;\n            this._header._parent = this._root;\n            this._header._left = this._root;\n            this._header._right = this._root;\n            return;\n        }\n        var curNode;\n        var minNode = this._header._left;\n        var compareToMin = this._cmp(minNode._key, key);\n        if (compareToMin === 0) {\n            minNode._value = value;\n            return;\n        }\n        else if (compareToMin > 0) {\n            minNode._left = new this._TreeNodeClass(key, value);\n            minNode._left._parent = minNode;\n            curNode = minNode._left;\n            this._header._left = curNode;\n        }\n        else {\n            var maxNode = this._header._right;\n            var compareToMax = this._cmp(maxNode._key, key);\n            if (compareToMax === 0) {\n                maxNode._value = value;\n                return;\n            }\n            else if (compareToMax < 0) {\n                maxNode._right = new this._TreeNodeClass(key, value);\n                maxNode._right._parent = maxNode;\n                curNode = maxNode._right;\n                this._header._right = curNode;\n            }\n            else {\n                if (hint !== undefined) {\n                    var iterNode = hint._node;\n                    if (iterNode !== this._header) {\n                        var iterCmpRes = this._cmp(iterNode._key, key);\n                        if (iterCmpRes === 0) {\n                            iterNode._value = value;\n                            return;\n                        }\n                        else /* istanbul ignore else */ if (iterCmpRes > 0) {\n                            var preNode = iterNode._pre();\n                            var preCmpRes = this._cmp(preNode._key, key);\n                            if (preCmpRes === 0) {\n                                preNode._value = value;\n                                return;\n                            }\n                            else if (preCmpRes < 0) {\n                                curNode = new this._TreeNodeClass(key, value);\n                                if (preNode._right === undefined) {\n                                    preNode._right = curNode;\n                                    curNode._parent = preNode;\n                                }\n                                else {\n                                    iterNode._left = curNode;\n                                    curNode._parent = iterNode;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (curNode === undefined) {\n                    curNode = this._root;\n                    while (true) {\n                        var cmpResult = this._cmp(curNode._key, key);\n                        if (cmpResult > 0) {\n                            if (curNode._left === undefined) {\n                                curNode._left = new this._TreeNodeClass(key, value);\n                                curNode._left._parent = curNode;\n                                curNode = curNode._left;\n                                break;\n                            }\n                            curNode = curNode._left;\n                        }\n                        else if (cmpResult < 0) {\n                            if (curNode._right === undefined) {\n                                curNode._right = new this._TreeNodeClass(key, value);\n                                curNode._right._parent = curNode;\n                                curNode = curNode._right;\n                                break;\n                            }\n                            curNode = curNode._right;\n                        }\n                        else {\n                            curNode._value = value;\n                            return;\n                        }\n                    }\n                }\n            }\n        }\n        this._length += 1;\n        return curNode;\n    };\n    /**\n     * @internal\n     */\n    TreeContainer.prototype._findElementNode = function (curNode, key) {\n        while (curNode) {\n            var cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                curNode = curNode._right;\n            }\n            else if (cmpResult > 0) {\n                curNode = curNode._left;\n            }\n            else\n                return curNode;\n        }\n        return curNode || this._header;\n    };\n    TreeContainer.prototype.clear = function () {\n        this._length = 0;\n        this._root = undefined;\n        this._header._parent = undefined;\n        this._header._left = this._header._right = undefined;\n    };\n    /**\n     * @description Update node's key by iterator.\n     * @param iter - The iterator you want to change.\n     * @param key - The key you want to update.\n     * @returns Whether the modification is successful.\n     * @example\n     * const st = new orderedSet([1, 2, 5]);\n     * const iter = st.find(2);\n     * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n     */\n    TreeContainer.prototype.updateKeyByIterator = function (iter, key) {\n        var node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        if (this._length === 1) {\n            node._key = key;\n            return true;\n        }\n        if (node === this._header._left) {\n            if (this._cmp(node._next()._key, key) > 0) {\n                node._key = key;\n                return true;\n            }\n            return false;\n        }\n        if (node === this._header._right) {\n            if (this._cmp(node._pre()._key, key) < 0) {\n                node._key = key;\n                return true;\n            }\n            return false;\n        }\n        var preKey = node._pre()._key;\n        if (this._cmp(preKey, key) >= 0)\n            return false;\n        var nextKey = node._next()._key;\n        if (this._cmp(nextKey, key) <= 0)\n            return false;\n        node._key = key;\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var index = 0;\n        var self = this;\n        this._inOrderTraversal(this._root, function (curNode) {\n            if (pos === index) {\n                self._eraseNode(curNode);\n                return true;\n            }\n            index += 1;\n            return false;\n        });\n        return this._length;\n    };\n    /**\n     * @description Remove the element of the specified key.\n     * @param key - The key you want to remove.\n     * @returns Whether erase successfully.\n     */\n    TreeContainer.prototype.eraseElementByKey = function (key) {\n        if (this._length === 0)\n            return false;\n        var curNode = this._findElementNode(this._root, key);\n        if (curNode === this._header)\n            return false;\n        this._eraseNode(curNode);\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByIterator = function (iter) {\n        var node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        var hasNoRight = node._right === undefined;\n        var isNormal = iter.iteratorType === 0 /* IteratorType.NORMAL */;\n        // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n        if (isNormal) {\n            // So we should move it to next when it's right is null.\n            if (hasNoRight)\n                iter.next();\n        }\n        else {\n            // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n            // So when it has right, or it is a leaf node we should move it to `next`.\n            if (!hasNoRight || node._left === undefined)\n                iter.next();\n        }\n        this._eraseNode(node);\n        return iter;\n    };\n    TreeContainer.prototype.forEach = function (callback) {\n        var e_1, _a;\n        var index = 0;\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var element = _c.value;\n                callback(element, index++, this);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    TreeContainer.prototype.getElementByPos = function (pos) {\n        var e_2, _a;\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var res;\n        var index = 0;\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var element = _c.value;\n                if (index === pos) {\n                    res = element;\n                    break;\n                }\n                index += 1;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return res;\n    };\n    /**\n     * @description Get the height of the tree.\n     * @returns Number about the height of the RB-tree.\n     */\n    TreeContainer.prototype.getHeight = function () {\n        if (this._length === 0)\n            return 0;\n        var traversal = function (curNode) {\n            if (!curNode)\n                return 0;\n            return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n        };\n        return traversal(this._root);\n    };\n    return TreeContainer;\n}(Container));\nexport default TreeContainer;\n", "import type TreeIterator from './TreeIterator';\nimport { TreeNode, TreeNodeColor, TreeNodeEnableIndex } from './TreeNode';\nimport { Container, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeContainer<K, V> extends Container<K | [K, V]> {\n  /**\n   * @internal\n   */\n  protected _root: TreeNode<K, V> | undefined = undefined;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected readonly _cmp: (x: K, y: K) => number;\n  /**\n   * @internal\n   */\n  protected readonly _TreeNodeClass: typeof TreeNode | typeof TreeNodeEnableIndex;\n  /**\n   * @internal\n   */\n  protected readonly _eraseNode: (curNode: TreeNode<K, V>) => void;\n  /**\n   * @internal\n   */\n  protected _set: (key: K, value: V, hint?: TreeIterator<K, V>) => number;\n  /**\n   * @internal\n   */\n  protected constructor(\n    cmp: (x: K, y: K) => number =\n    function (x: K, y: K) {\n      if (x < y) return -1;\n      if (x > y) return 1;\n      return 0;\n    },\n    enableIndex = false\n  ) {\n    super();\n    this._cmp = cmp;\n    if (enableIndex) {\n      this._TreeNodeClass = TreeNodeEnableIndex;\n      this._set = function (key, value, hint) {\n        const curNode = this._preSet(key, value, hint);\n        if (curNode) {\n          let p = curNode._parent as TreeNodeEnableIndex<K, V>;\n          while (p !== this._header) {\n            p._subTreeSize += 1;\n            p = p._parent as TreeNodeEnableIndex<K, V>;\n          }\n          const nodeList = this._insertNodeSelfBalance(curNode);\n          if (nodeList) {\n            const {\n              parentNode,\n              grandParent,\n              curNode\n            } = nodeList as unknown as Record<string, TreeNodeEnableIndex<K, V>>;\n            parentNode._recount();\n            grandParent._recount();\n            curNode._recount();\n          }\n        }\n        return this._length;\n      };\n      this._eraseNode = function (curNode) {\n        let p = this._preEraseNode(curNode) as TreeNodeEnableIndex<K, V>;\n        while (p !== this._header) {\n          p._subTreeSize -= 1;\n          p = p._parent as TreeNodeEnableIndex<K, V>;\n        }\n      };\n    } else {\n      this._TreeNodeClass = TreeNode;\n      this._set = function (key, value, hint) {\n        const curNode = this._preSet(key, value, hint);\n        if (curNode) this._insertNodeSelfBalance(curNode);\n        return this._length;\n      };\n      this._eraseNode = this._preEraseNode;\n    }\n    this._header = new this._TreeNodeClass();\n  }\n  /**\n   * @internal\n   */\n  protected _lowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        resNode = curNode;\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _upperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult <= 0) {\n        curNode = curNode._right;\n      } else {\n        resNode = curNode;\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseLowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseUpperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else {\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode === this._header) return;\n      if (curNode._color === TreeNodeColor.RED) {\n        curNode._color = TreeNodeColor.BLACK;\n        return;\n      }\n      if (curNode === parentNode._left) {\n        const brother = parentNode._right!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateLeft();\n          } else parentNode._rotateLeft();\n        } else {\n          if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._right._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateLeft();\n            } else parentNode._rotateLeft();\n            return;\n          } else if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._left._color = TreeNodeColor.BLACK;\n            brother._rotateRight();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      } else {\n        const brother = parentNode._left!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateRight();\n          } else parentNode._rotateRight();\n        } else {\n          if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._left._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateRight();\n            } else parentNode._rotateRight();\n            return;\n          } else if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._right._color = TreeNodeColor.BLACK;\n            brother._rotateLeft();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      }\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _preEraseNode(curNode: TreeNode<K, V>) {\n    if (this._length === 1) {\n      this.clear();\n      return this._header;\n    }\n    let swapNode = curNode;\n    while (swapNode._left || swapNode._right) {\n      if (swapNode._right) {\n        swapNode = swapNode._right;\n        while (swapNode._left) swapNode = swapNode._left;\n      } else {\n        swapNode = swapNode._left!;\n      }\n      [curNode._key, swapNode._key] = [swapNode._key, curNode._key];\n      [curNode._value, swapNode._value] = [swapNode._value, curNode._value];\n      curNode = swapNode;\n    }\n    if (this._header._left === swapNode) {\n      this._header._left = swapNode._parent;\n    } else if (this._header._right === swapNode) {\n      this._header._right = swapNode._parent;\n    }\n    this._eraseNodeSelfBalance(swapNode);\n    const _parent = swapNode._parent!;\n    if (swapNode === _parent._left) {\n      _parent._left = undefined;\n    } else _parent._right = undefined;\n    this._length -= 1;\n    this._root!._color = TreeNodeColor.BLACK;\n    return _parent;\n  }\n  /**\n   * @internal\n   */\n  protected _inOrderTraversal(\n    curNode: TreeNode<K, V> | undefined,\n    callback: (curNode: TreeNode<K, V>) => boolean\n  ): boolean {\n    if (curNode === undefined) return false;\n    const ifReturn = this._inOrderTraversal(curNode._left, callback);\n    if (ifReturn) return true;\n    if (callback(curNode)) return true;\n    return this._inOrderTraversal(curNode._right, callback);\n  }\n  /**\n   * @internal\n   */\n  protected _insertNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode._color === TreeNodeColor.BLACK) return;\n      const grandParent = parentNode._parent!;\n      if (parentNode === grandParent._left) {\n        const uncle = grandParent._right;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._right) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) curNode._left._parent = parentNode;\n          if (curNode._right) curNode._right._parent = grandParent;\n          parentNode._right = curNode._left;\n          grandParent._left = curNode._right;\n          curNode._left = parentNode;\n          curNode._right = grandParent;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n          return { parentNode, grandParent, curNode };\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateRight();\n          } else grandParent._rotateRight();\n          grandParent._color = TreeNodeColor.RED;\n        }\n      } else {\n        const uncle = grandParent._left;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._left) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) curNode._left._parent = grandParent;\n          if (curNode._right) curNode._right._parent = parentNode;\n          grandParent._right = curNode._left;\n          parentNode._left = curNode._right;\n          curNode._left = grandParent;\n          curNode._right = parentNode;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n          return { parentNode, grandParent, curNode };\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateLeft();\n          } else grandParent._rotateLeft();\n          grandParent._color = TreeNodeColor.RED;\n        }\n      }\n      return;\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _preSet(key: K, value?: V, hint?: TreeIterator<K, V>) {\n    if (this._root === undefined) {\n      this._length += 1;\n      this._root = new this._TreeNodeClass(key, value);\n      this._root._color = TreeNodeColor.BLACK;\n      this._root._parent = this._header;\n      this._header._parent = this._root;\n      this._header._left = this._root;\n      this._header._right = this._root;\n      return;\n    }\n    let curNode;\n    const minNode = this._header._left!;\n    const compareToMin = this._cmp(minNode._key!, key);\n    if (compareToMin === 0) {\n      minNode._value = value;\n      return;\n    } else if (compareToMin > 0) {\n      minNode._left = new this._TreeNodeClass(key, value);\n      minNode._left._parent = minNode;\n      curNode = minNode._left;\n      this._header._left = curNode;\n    } else {\n      const maxNode = this._header._right!;\n      const compareToMax = this._cmp(maxNode._key!, key);\n      if (compareToMax === 0) {\n        maxNode._value = value;\n        return;\n      } else if (compareToMax < 0) {\n        maxNode._right = new this._TreeNodeClass(key, value);\n        maxNode._right._parent = maxNode;\n        curNode = maxNode._right;\n        this._header._right = curNode;\n      } else {\n        if (hint !== undefined) {\n          const iterNode = hint._node;\n          if (iterNode !== this._header) {\n            const iterCmpRes = this._cmp(iterNode._key!, key);\n            if (iterCmpRes === 0) {\n              iterNode._value = value;\n              return;\n            } else /* istanbul ignore else */ if (iterCmpRes > 0) {\n              const preNode = iterNode._pre();\n              const preCmpRes = this._cmp(preNode._key!, key);\n              if (preCmpRes === 0) {\n                preNode._value = value;\n                return;\n              } else if (preCmpRes < 0) {\n                curNode = new this._TreeNodeClass(key, value);\n                if (preNode._right === undefined) {\n                  preNode._right = curNode;\n                  curNode._parent = preNode;\n                } else {\n                  iterNode._left = curNode;\n                  curNode._parent = iterNode;\n                }\n              }\n            }\n          }\n        }\n        if (curNode === undefined) {\n          curNode = this._root;\n          while (true) {\n            const cmpResult = this._cmp(curNode._key!, key);\n            if (cmpResult > 0) {\n              if (curNode._left === undefined) {\n                curNode._left = new this._TreeNodeClass(key, value);\n                curNode._left._parent = curNode;\n                curNode = curNode._left;\n                break;\n              }\n              curNode = curNode._left;\n            } else if (cmpResult < 0) {\n              if (curNode._right === undefined) {\n                curNode._right = new this._TreeNodeClass(key, value);\n                curNode._right._parent = curNode;\n                curNode = curNode._right;\n                break;\n              }\n              curNode = curNode._right;\n            } else {\n              curNode._value = value;\n              return;\n            }\n          }\n        }\n      }\n    }\n    this._length += 1;\n    return curNode;\n  }\n  /**\n   * @internal\n   */\n  protected _findElementNode(curNode: TreeNode<K, V> | undefined, key: K) {\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return curNode || this._header;\n  }\n  clear() {\n    this._length = 0;\n    this._root = undefined;\n    this._header._parent = undefined;\n    this._header._left = this._header._right = undefined;\n  }\n  /**\n   * @description Update node's key by iterator.\n   * @param iter - The iterator you want to change.\n   * @param key - The key you want to update.\n   * @returns Whether the modification is successful.\n   * @example\n   * const st = new orderedSet([1, 2, 5]);\n   * const iter = st.find(2);\n   * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n   */\n  updateKeyByIterator(iter: TreeIterator<K, V>, key: K): boolean {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    if (this._length === 1) {\n      node._key = key;\n      return true;\n    }\n    if (node === this._header._left) {\n      if (this._cmp(node._next()._key!, key) > 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    if (node === this._header._right) {\n      if (this._cmp(node._pre()._key!, key) < 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    const preKey = node._pre()._key!;\n    if (this._cmp(preKey, key) >= 0) return false;\n    const nextKey = node._next()._key!;\n    if (this._cmp(nextKey, key) <= 0) return false;\n    node._key = key;\n    return true;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let index = 0;\n    const self = this;\n    this._inOrderTraversal(\n      this._root,\n      function (curNode) {\n        if (pos === index) {\n          self._eraseNode(curNode);\n          return true;\n        }\n        index += 1;\n        return false;\n      });\n    return this._length;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key: K) {\n    if (this._length === 0) return false;\n    const curNode = this._findElementNode(this._root, key);\n    if (curNode === this._header) return false;\n    this._eraseNode(curNode);\n    return true;\n  }\n  eraseElementByIterator(iter: TreeIterator<K, V>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    const hasNoRight = node._right === undefined;\n    const isNormal = iter.iteratorType === IteratorType.NORMAL;\n    // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n    if (isNormal) {\n      // So we should move it to next when it's right is null.\n      if (hasNoRight) iter.next();\n    } else {\n      // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n      // So when it has right, or it is a leaf node we should move it to `next`.\n      if (!hasNoRight || node._left === undefined) iter.next();\n    }\n    this._eraseNode(node);\n    return iter;\n  }\n  forEach(callback: (element: K | [K, V], index: number, tree: TreeContainer<K, V>) => void) {\n    let index = 0;\n    for (const element of this) callback(element, index++, this);\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let res;\n    let index = 0;\n    for (const element of this) {\n      if (index === pos) {\n        res = element;\n        break;\n      }\n      index += 1;\n    }\n    return <K | [K, V]>res;\n  }\n  /**\n   * @description Get the height of the tree.\n   * @returns Number about the height of the RB-tree.\n   */\n  getHeight() {\n    if (this._length === 0) return 0;\n    const traversal =\n      function (curNode: TreeNode<K, V> | undefined): number {\n        if (!curNode) return 0;\n        return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n      };\n    return traversal(this._root);\n  }\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element less than the given key.\n   */\n  abstract reverseUpperBound(key: K): TreeIterator<K, V>;\n  /**\n   * @description Union the other tree to self.\n   * @param other - The other tree container you want to merge.\n   * @returns The size of the tree after union.\n   */\n  abstract union(other: TreeContainer<K, V>): number;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not greater than the given key.\n   */\n  abstract reverseLowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not less than the given key.\n   */\n  abstract lowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element greater than the given key.\n   */\n  abstract upperBound(key: K): TreeIterator<K, V>;\n}\n\nexport default TreeContainer;\n"]}