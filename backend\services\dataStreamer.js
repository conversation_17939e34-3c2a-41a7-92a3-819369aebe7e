// 数据流推送服务 - 优化数据推送性能
const EventEmitter = require('events');

class DataStreamer extends EventEmitter {
    constructor() {
        super();
        this.isStreaming = false;
        this.streamInterval = null;
        this.dataBuffer = new Map(); // 数据缓冲区
        this.lastBroadcast = new Map(); // 上次广播的数据
        this.updateFrequency = 50; // 50ms更新频率
        this.batchSize = 10; // 批量处理大小
        this.compressionEnabled = true; // 数据压缩
    }

    // 开始数据流
    start() {
        if (this.isStreaming) {
            console.log('⚠️ 数据流已在运行中');
            return;
        }

        this.isStreaming = true;
        this.streamInterval = setInterval(() => {
            this.processDataStream();
        }, this.updateFrequency);

        console.log(`🔄 数据流服务启动 (${this.updateFrequency}ms间隔)`);
        this.emit('stream-started');
    }

    // 停止数据流
    stop() {
        if (!this.isStreaming) {
            return;
        }

        this.isStreaming = false;
        if (this.streamInterval) {
            clearInterval(this.streamInterval);
            this.streamInterval = null;
        }

        console.log('🛑 数据流服务已停止');
        this.emit('stream-stopped');
    }

    // 处理数据流
    processDataStream() {
        try {
            // 检查是否有新数据需要推送
            const pendingUpdates = this.collectPendingUpdates();
            
            if (pendingUpdates.length > 0) {
                // 批量处理数据更新
                const batchedUpdates = this.batchUpdates(pendingUpdates);
                
                // 发送数据更新事件
                this.emit('data-batch-ready', batchedUpdates);
                
                // 更新最后广播记录
                this.updateLastBroadcast(batchedUpdates);
            }
        } catch (error) {
            console.error('❌ 数据流处理错误:', error);
            this.emit('stream-error', error);
        }
    }

    // 收集待处理的更新
    collectPendingUpdates() {
        const updates = [];
        
        // 检查数据缓冲区中的新数据
        for (const [key, data] of this.dataBuffer) {
            const lastData = this.lastBroadcast.get(key);
            
            if (!lastData || this.hasDataChanged(data, lastData)) {
                updates.push({
                    key,
                    data: this.compressionEnabled ? this.compressData(data) : data,
                    timestamp: new Date().toISOString(),
                    type: this.getUpdateType(key)
                });
            }
        }
        
        return updates;
    }

    // 批量处理更新
    batchUpdates(updates) {
        const batches = [];
        
        for (let i = 0; i < updates.length; i += this.batchSize) {
            const batch = updates.slice(i, i + this.batchSize);
            batches.push({
                id: this.generateBatchId(),
                updates: batch,
                timestamp: new Date().toISOString(),
                size: batch.length
            });
        }
        
        return batches;
    }

    // 检查数据是否有变化
    hasDataChanged(newData, oldData) {
        if (!oldData) return true;
        
        // 简单的JSON比较（可以优化为更高效的比较）
        return JSON.stringify(newData) !== JSON.stringify(oldData);
    }

    // 压缩数据（简化版）
    compressData(data) {
        // 移除不必要的字段，只保留关键数据
        if (data && typeof data === 'object') {
            const compressed = {};
            
            // 只保留变化的关键字段
            const keyFields = ['id', 'temperature', 'humidity', 'light', 'smoke', 'status', 'alertLevel'];
            
            keyFields.forEach(field => {
                if (data[field] !== undefined) {
                    compressed[field] = data[field];
                }
            });
            
            return compressed;
        }
        
        return data;
    }

    // 获取更新类型
    getUpdateType(key) {
        if (key.startsWith('node-')) {
            return 'node-data';
        } else if (key.startsWith('alert-')) {
            return 'alert-data';
        } else if (key === 'system-status') {
            return 'system-data';
        }
        return 'unknown';
    }

    // 生成批次ID
    generateBatchId() {
        return `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    // 更新最后广播记录
    updateLastBroadcast(batches) {
        batches.forEach(batch => {
            batch.updates.forEach(update => {
                this.lastBroadcast.set(update.key, update.data);
            });
        });
    }

    // 添加数据到缓冲区
    addData(key, data) {
        this.dataBuffer.set(key, {
            ...data,
            _timestamp: Date.now()
        });
    }

    // 移除过期数据
    cleanupExpiredData(maxAge = 60000) { // 默认60秒
        const now = Date.now();
        
        for (const [key, data] of this.dataBuffer) {
            if (data._timestamp && (now - data._timestamp) > maxAge) {
                this.dataBuffer.delete(key);
            }
        }
    }

    // 获取流统计信息
    getStreamStats() {
        return {
            isStreaming: this.isStreaming,
            updateFrequency: this.updateFrequency,
            bufferSize: this.dataBuffer.size,
            lastBroadcastCount: this.lastBroadcast.size,
            batchSize: this.batchSize,
            compressionEnabled: this.compressionEnabled,
            uptime: this.isStreaming ? Date.now() - this.startTime : 0
        };
    }

    // 设置更新频率
    setUpdateFrequency(frequency) {
        if (frequency < 10 || frequency > 1000) {
            throw new Error('更新频率必须在10-1000ms之间');
        }
        
        this.updateFrequency = frequency;
        
        // 如果正在运行，重启以应用新频率
        if (this.isStreaming) {
            this.stop();
            this.start();
        }
        
        console.log(`🔄 更新频率已设置为 ${frequency}ms`);
    }

    // 设置批量大小
    setBatchSize(size) {
        if (size < 1 || size > 100) {
            throw new Error('批量大小必须在1-100之间');
        }
        
        this.batchSize = size;
        console.log(`📦 批量大小已设置为 ${size}`);
    }

    // 启用/禁用压缩
    setCompression(enabled) {
        this.compressionEnabled = enabled;
        console.log(`🗜️ 数据压缩已${enabled ? '启用' : '禁用'}`);
    }

    // 清空缓冲区
    clearBuffer() {
        this.dataBuffer.clear();
        this.lastBroadcast.clear();
        console.log('🧹 数据缓冲区已清空');
    }
}

module.exports = DataStreamer;
