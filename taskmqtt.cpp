#include "taskmqtt.h"

taskMqtt::taskMqtt(QObject *parent)
    : QObject{parent}
{

}
int i = 0;
void taskMqtt::TaskSendToCloud()
{
    QString mqttMessage;

    // 获取当前UTC时间戳（华为云IoT平台标准格式：yyyyMMdd'T'HHmmss'Z'）
    QString timestamp = QDateTime::currentDateTimeUtc().toString("yyyyMMddTHHmmssZ");

    // 华为云IoT平台标准属性上报消息格式（根据官方文档）
    mqttMessage = "{"
                  "\"services\": ["
                  "{"
                  "\"service_id\": \"SensorData\","
                  "\"properties\": {"
                  "\"temperature\": " + stringTemp1 + ","
                  "\"humidity\": " + stringHumi1 + ","
                  "\"light\": " + stringLight1 + ","
                  "\"smoke\": " + stringSmog1
                  "},"
                  "\"event_time\": \"" + timestamp + "\""
                  "}"
                  "]"
                  "}";

    // 调试信息：打印发送的消息
    qDebug() << "发送到华为云的消息:" << mqttMessage;
    qDebug() << "发送主题:" << m_strPubTopic;

    // 创建华为云IoT平台MQTT消息对象
    QMQTT::Message send_msg(136, m_strPubTopic, mqttMessage.toLocal8Bit(), 0);
    mqttClient->publish(send_msg);
}

void taskMqtt::Init_HuaweiCloud_MQTT()
{
    // ========== 华为云IoT平台连接参数配置接口 ==========
    // 请在此处填入您的华为云IoT设备连接信息

    QString m_strDeviceId = "685a734ad582f2001834985f_loong_1";           // 设备ID - 请填入您的设备ID
    QString m_strDeviceSecret = "3246a4eab894b351f7d199a8d2f9aa302fece2d1204e126c429e044f1a1a082a";       // 设备密钥 - 请填入您的设备密钥

    // 华为云IoT平台MQTT服务器地址格式
    QString m_strHostName = "5930c00e73.st1.iotda-device.cn-north-4.myhuaweicloud.com";

    // 华为云IoT平台主题配置
    m_strPubTopic = "$oc/devices/" + m_strDeviceId + "/sys/properties/report";     // 属性上报主题
    m_strSubTopic = "$oc/devices/" + m_strDeviceId + "/sys/properties/set";        // 属性设置主题

    // 客户端ID配置
    QString clientId = "685a734ad582f2001834985f_loong_1_0_0_2025062508";

    // MQTT连接参数设置
    mqttClient->setHostName(m_strHostName);     // 设置服务器地址
    mqttClient->setPort(8883);                  // 华为云IoT平台MQTT SSL端口
    mqttClient->setUsername(m_strDeviceId);     // 用户名为设备ID
    mqttClient->setPassword(m_strDeviceSecret.toLocal8Bit()); // 密码为设备密钥，转换为QByteArray
    mqttClient->setClientId(clientId);          // 客户端ID
    mqttClient->setKeepAlive(120);              // 心跳间隔120秒

    // 绑定连接成功槽函数
    connect(mqttClient, SIGNAL(connected()), this, SLOT(Slot_MqttConnectSuccess()));

    // 连接到华为云IoT平台
    mqttClient->connectToHost();
}

void taskMqtt::Slot_MqttConnectSuccess()
{
    emit Signal_MqttConnectSuccess();
}
