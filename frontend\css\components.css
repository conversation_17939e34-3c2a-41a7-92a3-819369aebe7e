/* 组件样式 - 苹果风格设计 */

/* 节点网格布局 */
.nodes-section {
    margin-bottom: var(--spacing-xxl);
}

.nodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* 节点卡片 */
.node-card {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.node-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.node-card:active {
    transform: translateY(-2px) scale(1.01);
    transition: all var(--transition-fast);
}

.node-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    opacity: 0.8;
}

.node-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left var(--transition-slow);
}

.node-card:hover::after {
    left: 100%;
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.node-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.node-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.node-status.online {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-color);
}

.node-status.offline {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
}

.node-status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* 传感器数据网格 */
.sensor-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.sensor-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--background-primary);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

.sensor-item:hover {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.sensor-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.sensor-item:hover::before {
    transform: scaleX(1);
}

.sensor-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.sensor-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.sensor-unit {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 图表容器 */
.charts-section {
    margin-bottom: var(--spacing-xxl);
}

/* 图表控制器 */
.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--glass-background);
    border-radius: var(--radius-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    overflow-x: auto;
}

.chart-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.chart-tab:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.chart-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.chart-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left var(--transition-normal);
}

.chart-tab:hover::before {
    left: 100%;
}

/* 图表网格 */
.charts-grid {
    position: relative;
    height: 400px;
}

.chart-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    overflow: hidden;
    transition: all var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.chart-container.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.chart-container:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        var(--primary-color) 0%,
        var(--secondary-color) 100%);
    opacity: 0.02;
    pointer-events: none;
}

/* 警告区域 */
.alerts-section {
    margin-bottom: var(--spacing-xxl);
}

.alerts-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.alert-item {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.2s ease;
}

.alert-item:hover {
    transform: translateX(4px);
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.alert-icon.warning {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
}

.alert-icon.error {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
}

.alert-icon.info {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.alert-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.alert-time {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 按钮样式 */
.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--background-primary);
    color: var(--text-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--text-secondary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--spacing-sm);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state-text {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-sm);
}

.empty-state-subtext {
    font-size: var(--font-size-sm);
    opacity: 0.7;
}

/* 主题切换器 */
.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.theme-toggle:active {
    transform: scale(0.95);
    transition: all var(--transition-fast);
}

.theme-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.theme-toggle:hover::before {
    opacity: 0.1;
}

.theme-icon {
    font-size: var(--font-size-lg);
    transition: all var(--transition-normal);
    position: relative;
    z-index: 1;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(180deg);
}

/* 响应式主题切换器 */
@media (max-width: 768px) {
    .theme-toggle {
        width: 36px;
        height: 36px;
    }

    .theme-icon {
        font-size: var(--font-size-md);
    }
}

/* 节点详情模态框 */
.node-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.node-details-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    min-width: 400px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
}

.modal-close:hover {
    background: var(--error-color);
    color: white;
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--glass-border);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.detail-value {
    font-weight: 600;
    color: var(--text-primary);
}

.detail-value.online {
    color: var(--success-color);
}

.detail-value.offline {
    color: var(--error-color);
}

.detail-value.alert-normal {
    color: var(--success-color);
}

.detail-value.alert-warning {
    color: var(--warning-color);
}

.detail-value.alert-error {
    color: var(--error-color);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--glass-border);
}

.btn-primary, .btn-secondary {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: var(--text-tertiary);
    color: white;
}

/* 警告高亮效果 */
.alert-item.highlight {
    background: var(--primary-color);
    color: white;
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
    animation: alertHighlight 2s ease-out;
}

@keyframes alertHighlight {
    0% {
        background: var(--primary-color);
        transform: scale(1.05);
    }
    50% {
        background: var(--primary-color-light);
        transform: scale(1.02);
    }
    100% {
        background: var(--glass-background);
        transform: scale(1);
    }
}

/* 警告级别颜色 */
.alert-item.error {
    border-left: 4px solid var(--error-color);
}

.alert-item.warning {
    border-left: 4px solid var(--warning-color);
}

.alert-item.info {
    border-left: 4px solid var(--info-color);
}

/* 警告通知弹窗 */
.alert-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-background);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-xl);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    max-width: 300px;
    z-index: 9999;
    transform: translateX(100%);
    transition: all var(--transition-normal);
}

.alert-notification.show {
    transform: translateX(0);
}

.alert-notification.error {
    border-left: 4px solid var(--error-color);
}

.alert-notification.warning {
    border-left: 4px solid var(--warning-color);
}

.alert-notification.info {
    border-left: 4px solid var(--info-color);
}

/* 加载屏幕 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    animation: fadeIn 0.8s ease-out;
}

.loading-logo {
    margin-bottom: var(--spacing-xl);
}

.logo-icon {
    font-size: var(--font-size-xxxl);
    margin-bottom: var(--spacing-md);
    animation: bounce 2s infinite;
}

.loading-logo h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.loading-spinner {
    position: relative;
    width: 60px;
    height: 60px;
    margin: var(--spacing-xl) auto;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
    border-top-color: var(--secondary-color);
    animation-duration: 1.5s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
    border-top-color: var(--success-color);
    animation-duration: 2s;
}

.loading-text {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin-top: var(--spacing-lg);
    animation: pulse 2s infinite;
}
