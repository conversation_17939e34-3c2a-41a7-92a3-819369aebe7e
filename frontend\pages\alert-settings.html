<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警告设置 - 龙芯智能终端管理系统</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/alert-settings.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 头部 -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <div class="header-left">
                        <button class="back-button" id="backButton">
                            <span class="back-icon">←</span>
                            <span>返回</span>
                        </button>
                        <h1 class="page-title">警告设置</h1>
                    </div>
                    <div class="header-right">
                        <div class="theme-toggle" id="themeToggle">
                            <span class="theme-icon">🌙</span>
                        </div>
                        <div class="time-display" id="currentTime"></div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容 -->
        <main class="main-content">
            <div class="container">
                <!-- 通知设置 -->
                <section class="settings-section">
                    <h2 class="section-title">通知设置</h2>
                    <div class="settings-card">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h3 class="setting-name">浏览器通知</h3>
                                <p class="setting-desc">在浏览器中显示警告通知</p>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="browserNotifications" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h3 class="setting-name">声音提示</h3>
                                <p class="setting-desc">播放警告提示音</p>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="soundAlerts" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h3 class="setting-name">弹窗警告</h3>
                                <p class="setting-desc">在页面中显示警告弹窗</p>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="popupAlerts" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h3 class="setting-name">邮件通知</h3>
                                <p class="setting-desc">发送警告邮件（需要配置邮箱）</p>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="emailAlerts">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 阈值设置 -->
                <section class="settings-section">
                    <h2 class="section-title">传感器阈值设置</h2>
                    <div class="threshold-grid">
                        <!-- 温度阈值 -->
                        <div class="threshold-card">
                            <div class="threshold-header">
                                <div class="sensor-icon">🌡️</div>
                                <h3 class="sensor-name">温度</h3>
                                <span class="sensor-unit">°C</span>
                            </div>
                            <div class="threshold-controls">
                                <div class="threshold-input">
                                    <label>最小值</label>
                                    <input type="number" id="tempMin" value="0" min="-50" max="100">
                                </div>
                                <div class="threshold-input">
                                    <label>最大值</label>
                                    <input type="number" id="tempMax" value="50" min="-50" max="100">
                                </div>
                            </div>
                            <div class="threshold-preview">
                                <span class="preview-text">正常范围: <span id="tempRange">0°C - 50°C</span></span>
                            </div>
                        </div>

                        <!-- 湿度阈值 -->
                        <div class="threshold-card">
                            <div class="threshold-header">
                                <div class="sensor-icon">💧</div>
                                <h3 class="sensor-name">湿度</h3>
                                <span class="sensor-unit">%RH</span>
                            </div>
                            <div class="threshold-controls">
                                <div class="threshold-input">
                                    <label>最小值</label>
                                    <input type="number" id="humiMin" value="10" min="0" max="100">
                                </div>
                                <div class="threshold-input">
                                    <label>最大值</label>
                                    <input type="number" id="humiMax" value="500" min="0" max="1000">
                                </div>
                            </div>
                            <div class="threshold-preview">
                                <span class="preview-text">正常范围: <span id="humiRange">10%RH - 500%RH</span></span>
                            </div>
                        </div>

                        <!-- 光照阈值 -->
                        <div class="threshold-card">
                            <div class="threshold-header">
                                <div class="sensor-icon">💡</div>
                                <h3 class="sensor-name">光照</h3>
                                <span class="sensor-unit">lm</span>
                            </div>
                            <div class="threshold-controls">
                                <div class="threshold-input">
                                    <label>最小值</label>
                                    <input type="number" id="lightMin" value="10" min="0" max="2000">
                                </div>
                                <div class="threshold-input">
                                    <label>最大值</label>
                                    <input type="number" id="lightMax" value="800" min="0" max="2000">
                                </div>
                            </div>
                            <div class="threshold-preview">
                                <span class="preview-text">正常范围: <span id="lightRange">10lm - 800lm</span></span>
                            </div>
                        </div>

                        <!-- 烟雾阈值 -->
                        <div class="threshold-card">
                            <div class="threshold-header">
                                <div class="sensor-icon">💨</div>
                                <h3 class="sensor-name">烟雾</h3>
                                <span class="sensor-unit">mg</span>
                            </div>
                            <div class="threshold-controls">
                                <div class="threshold-input">
                                    <label>最小值</label>
                                    <input type="number" id="smokeMin" value="0" min="0" max="1000">
                                </div>
                                <div class="threshold-input">
                                    <label>最大值</label>
                                    <input type="number" id="smokeMax" value="600" min="0" max="1000">
                                </div>
                            </div>
                            <div class="threshold-preview">
                                <span class="preview-text">正常范围: <span id="smokeRange">0mg - 600mg</span></span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 警告级别设置 -->
                <section class="settings-section">
                    <h2 class="section-title">警告级别设置</h2>
                    <div class="level-settings">
                        <div class="level-item">
                            <div class="level-info">
                                <span class="level-icon error">❌</span>
                                <div class="level-details">
                                    <h3>错误级别</h3>
                                    <p>严重超出正常范围，需要立即处理</p>
                                </div>
                            </div>
                            <div class="level-actions">
                                <button class="test-btn" data-level="error">测试</button>
                            </div>
                        </div>

                        <div class="level-item">
                            <div class="level-info">
                                <span class="level-icon warning">⚠️</span>
                                <div class="level-details">
                                    <h3>警告级别</h3>
                                    <p>轻微超出正常范围，建议关注</p>
                                </div>
                            </div>
                            <div class="level-actions">
                                <button class="test-btn" data-level="warning">测试</button>
                            </div>
                        </div>

                        <div class="level-item">
                            <div class="level-info">
                                <span class="level-icon info">ℹ️</span>
                                <div class="level-details">
                                    <h3>信息级别</h3>
                                    <p>一般信息提示，无需特殊处理</p>
                                </div>
                            </div>
                            <div class="level-actions">
                                <button class="test-btn" data-level="info">测试</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 操作按钮 -->
                <section class="actions-section">
                    <div class="actions-grid">
                        <button class="action-btn primary" id="saveSettings">保存设置</button>
                        <button class="action-btn secondary" id="resetSettings">重置默认</button>
                        <button class="action-btn secondary" id="exportSettings">导出配置</button>
                        <button class="action-btn secondary" id="importSettings">导入配置</button>
                    </div>
                    <input type="file" id="importFile" accept=".json" style="display: none;">
                </section>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="../js/config.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/alert-settings.js"></script>
</body>
</html>
