{"files.associations": {"initializer_list": "cpp", "utility": "cpp", "xutility": "cpp", "atomic": "cpp", "system_error": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "format": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "locale": "cpp", "memory": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstring": "cpp", "xtr1common": "cpp"}}