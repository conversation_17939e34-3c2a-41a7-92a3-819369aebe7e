// API接口测试 - 验证所有RESTful API功能
const http = require('http');

const BASE_URL = 'http://127.0.0.1:3000';

// HTTP请求工具函数
function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, BASE_URL);
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        const req = http.request(url, options, (res) => {
            let body = '';
            res.on('data', chunk => body += chunk);
            res.on('end', () => {
                try {
                    const response = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: body ? JSON.parse(body) : null
                    };
                    resolve(response);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: body
                    });
                }
            });
        });

        req.on('error', reject);

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 测试函数
async function runApiTests() {
    console.log('🧪 开始API接口测试...\n');

    try {
        // 测试1: 系统健康检查
        console.log('💚 测试系统健康检查:');
        const healthResponse = await makeRequest('GET', '/api/health');
        console.log(`✅ 状态码: ${healthResponse.statusCode}`);
        console.log(`✅ 响应: ${healthResponse.data.message}`);

        // 测试2: 获取API文档
        console.log('\n📚 测试API文档:');
        const apiDocResponse = await makeRequest('GET', '/api');
        console.log(`✅ 状态码: ${apiDocResponse.statusCode}`);
        console.log(`✅ API名称: ${apiDocResponse.data.name}`);

        // 测试3: 获取所有节点
        console.log('\n📍 测试获取所有节点:');
        const nodesResponse = await makeRequest('GET', '/api/nodes');
        console.log(`✅ 状态码: ${nodesResponse.statusCode}`);
        console.log(`✅ 节点数量: ${nodesResponse.data.data.length}`);

        // 测试4: 获取单个节点
        console.log('\n🔍 测试获取单个节点:');
        const nodeResponse = await makeRequest('GET', '/api/nodes/1');
        console.log(`✅ 状态码: ${nodeResponse.statusCode}`);
        console.log(`✅ 节点名称: ${nodeResponse.data.data.name}`);

        // 测试5: 更新节点数据
        console.log('\n📊 测试更新节点数据:');
        const updateData = {
            temperature: 25.5,
            humidity: 60.2,
            light: 500,
            smoke: 20.0
        };
        const updateResponse = await makeRequest('POST', '/api/nodes/1/data', updateData);
        console.log(`✅ 状态码: ${updateResponse.statusCode}`);
        console.log(`✅ 更新结果: ${updateResponse.data.message}`);

        // 测试6: Qt格式数据
        console.log('\n🔄 测试Qt格式数据:');
        const qtData = { data: "2,600,28.0,65.0,25.0" };
        const qtResponse = await makeRequest('POST', '/api/nodes/qt-data', qtData);
        console.log(`✅ 状态码: ${qtResponse.statusCode}`);
        console.log(`✅ Qt数据处理: ${qtResponse.data.message}`);

        // 测试7: 获取节点历史数据
        console.log('\n📈 测试获取历史数据:');
        const historyResponse = await makeRequest('GET', '/api/nodes/1/history?hours=1&limit=10');
        console.log(`✅ 状态码: ${historyResponse.statusCode}`);
        console.log(`✅ 历史数据点: ${historyResponse.data.data.length}`);

        // 测试8: 获取警告配置
        console.log('\n⚠️ 测试获取警告配置:');
        const alertConfigResponse = await makeRequest('GET', '/api/alerts/config/1');
        console.log(`✅ 状态码: ${alertConfigResponse.statusCode}`);
        console.log(`✅ 温度范围: ${JSON.stringify(alertConfigResponse.data.data.tempRange)}`);

        // 测试9: 更新警告配置
        console.log('\n🔧 测试更新警告配置:');
        const newConfig = {
            tempRange: { min: 5, max: 45 },
            enabled: true
        };
        const configUpdateResponse = await makeRequest('POST', '/api/alerts/config/1', newConfig);
        console.log(`✅ 状态码: ${configUpdateResponse.statusCode}`);
        console.log(`✅ 配置更新: ${configUpdateResponse.data.message}`);

        // 测试10: 测试警告
        console.log('\n🚨 测试警告检测:');
        const alertTestData = {
            temperature: 50.0, // 超出范围
            humidity: 60.0,
            light: 500,
            smoke: 20.0
        };
        const alertTestResponse = await makeRequest('POST', '/api/alerts/test/1', alertTestData);
        console.log(`✅ 状态码: ${alertTestResponse.statusCode}`);
        console.log(`✅ 警告数量: ${alertTestResponse.data.data.alertCount}`);

        // 测试11: 获取警告摘要
        console.log('\n📋 测试获取警告摘要:');
        const alertSummaryResponse = await makeRequest('GET', '/api/alerts/summary?hours=1');
        console.log(`✅ 状态码: ${alertSummaryResponse.statusCode}`);
        console.log(`✅ 总警告数: ${alertSummaryResponse.data.data.total}`);

        // 测试12: 获取系统状态
        console.log('\n🖥️ 测试获取系统状态:');
        const systemStatusResponse = await makeRequest('GET', '/api/system/status');
        console.log(`✅ 状态码: ${systemStatusResponse.statusCode}`);
        console.log(`✅ 在线节点: ${systemStatusResponse.data.data.nodes.online}/${systemStatusResponse.data.data.nodes.total}`);

        // 测试13: 获取系统统计
        console.log('\n📊 测试获取系统统计:');
        const statsResponse = await makeRequest('GET', '/api/system/stats?hours=1');
        console.log(`✅ 状态码: ${statsResponse.statusCode}`);
        console.log(`✅ 总数据点: ${statsResponse.data.data.totalDataPoints}`);

        // 测试14: 获取系统信息
        console.log('\n ℹ️ 测试获取系统信息:');
        const infoResponse = await makeRequest('GET', '/api/system/info');
        console.log(`✅ 状态码: ${infoResponse.statusCode}`);
        console.log(`✅ 系统名称: ${infoResponse.data.data.name}`);

        // 测试15: 错误处理测试
        console.log('\n❌ 测试错误处理:');
        
        // 无效节点ID
        const invalidNodeResponse = await makeRequest('GET', '/api/nodes/999');
        console.log(`✅ 无效节点ID状态码: ${invalidNodeResponse.statusCode}`);
        
        // 无效数据格式
        const invalidDataResponse = await makeRequest('POST', '/api/nodes/1/data', { invalid: 'data' });
        console.log(`✅ 无效数据格式状态码: ${invalidDataResponse.statusCode}`);
        
        // 不存在的端点
        const notFoundResponse = await makeRequest('GET', '/api/nonexistent');
        console.log(`✅ 不存在端点状态码: ${notFoundResponse.statusCode}`);

        console.log('\n🎉 API接口测试完成！');
        console.log('📋 测试总结:');
        console.log('- 系统健康检查: ✅');
        console.log('- 节点管理API: ✅');
        console.log('- 数据更新API: ✅');
        console.log('- 历史数据API: ✅');
        console.log('- 警告配置API: ✅');
        console.log('- 系统状态API: ✅');
        console.log('- 错误处理: ✅');
        console.log('- 响应格式统一: ✅');

    } catch (error) {
        console.error('❌ API测试失败:', error.message);
        process.exit(1);
    }
}

// 等待服务器启动后运行测试
setTimeout(() => {
    runApiTests().then(() => {
        console.log('\n✨ 所有API测试通过！');
        process.exit(0);
    }).catch(error => {
        console.error('❌ 测试执行失败:', error);
        process.exit(1);
    });
}, 2000); // 等待2秒确保服务器启动
