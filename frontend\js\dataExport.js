// 数据导出工具 - 支持多种格式的数据导出
class DataExporter {
    constructor() {
        this.supportedFormats = ['json', 'csv', 'xlsx'];
    }

    // 导出图表数据
    exportChartData(format = 'json', sensorType = null) {
        const chartManager = window.app?.getManager('charts');
        if (!chartManager) {
            console.error('❌ 图表管理器不可用');
            return;
        }

        const data = this.prepareChartData(chartManager, sensorType);
        
        switch (format.toLowerCase()) {
            case 'json':
                this.exportAsJSON(data, sensorType);
                break;
            case 'csv':
                this.exportAsCSV(data, sensorType);
                break;
            case 'xlsx':
                this.exportAsXLSX(data, sensorType);
                break;
            default:
                console.error('❌ 不支持的导出格式:', format);
        }
    }

    // 准备图表数据
    prepareChartData(chartManager, sensorType) {
        if (sensorType) {
            // 导出单个传感器数据
            const chartData = chartManager.chartData.get(sensorType);
            return this.formatSingleSensorData(sensorType, chartData);
        } else {
            // 导出所有传感器数据
            const allData = {};
            chartManager.chartData.forEach((chartData, type) => {
                allData[type] = this.formatSingleSensorData(type, chartData);
            });
            return allData;
        }
    }

    // 格式化单个传感器数据
    formatSingleSensorData(sensorType, chartData) {
        if (!chartData) return null;

        const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
        
        return {
            sensorType,
            sensorName: sensorConfig.name,
            unit: sensorConfig.unit,
            exportTime: new Date().toISOString(),
            timeLabels: chartData.labels,
            nodeData: chartData.datasets.map((dataset, index) => ({
                nodeId: index + 1,
                nodeName: dataset.label,
                values: dataset.data,
                statistics: ChartUtils.calculateDataStatistics(dataset.data)
            })),
            summary: {
                totalDataPoints: chartData.labels.length,
                timeRange: {
                    start: chartData.labels[0] || null,
                    end: chartData.labels[chartData.labels.length - 1] || null
                }
            }
        };
    }

    // 导出为JSON格式
    exportAsJSON(data, sensorType) {
        const filename = sensorType 
            ? `sensor_data_${sensorType}_${this.getTimestamp()}.json`
            : `all_sensor_data_${this.getTimestamp()}.json`;
        
        const jsonString = JSON.stringify(data, null, 2);
        this.downloadFile(jsonString, filename, 'application/json');
        
        console.log(`📄 JSON数据已导出: ${filename}`);
    }

    // 导出为CSV格式
    exportAsCSV(data, sensorType) {
        let csvContent = '';
        
        if (sensorType) {
            // 单个传感器CSV
            csvContent = this.generateSingleSensorCSV(data);
        } else {
            // 所有传感器CSV
            csvContent = this.generateAllSensorsCSV(data);
        }
        
        const filename = sensorType 
            ? `sensor_data_${sensorType}_${this.getTimestamp()}.csv`
            : `all_sensor_data_${this.getTimestamp()}.csv`;
        
        this.downloadFile(csvContent, filename, 'text/csv');
        
        console.log(`📊 CSV数据已导出: ${filename}`);
    }

    // 生成单个传感器CSV
    generateSingleSensorCSV(data) {
        if (!data || !data.timeLabels) return '';

        let csv = `时间,节点1,节点2,节点3\n`;
        
        for (let i = 0; i < data.timeLabels.length; i++) {
            const time = data.timeLabels[i];
            const values = data.nodeData.map(node => 
                node.values[i] !== undefined ? node.values[i] : ''
            );
            csv += `${time},${values.join(',')}\n`;
        }
        
        return csv;
    }

    // 生成所有传感器CSV
    generateAllSensorsCSV(data) {
        let csv = '时间,传感器类型,节点1,节点2,节点3\n';
        
        Object.keys(data).forEach(sensorType => {
            const sensorData = data[sensorType];
            if (!sensorData || !sensorData.timeLabels) return;
            
            for (let i = 0; i < sensorData.timeLabels.length; i++) {
                const time = sensorData.timeLabels[i];
                const values = sensorData.nodeData.map(node => 
                    node.values[i] !== undefined ? node.values[i] : ''
                );
                csv += `${time},${sensorData.sensorName},${values.join(',')}\n`;
            }
        });
        
        return csv;
    }

    // 导出为XLSX格式（简化版，实际需要第三方库）
    exportAsXLSX(data, sensorType) {
        // 这里简化为CSV格式，实际应用中可以使用SheetJS等库
        console.warn('⚠️ XLSX导出功能需要第三方库支持，当前导出为CSV格式');
        this.exportAsCSV(data, sensorType);
    }

    // 导出节点状态报告
    exportNodeStatusReport() {
        const nodeManager = window.app?.getManager('nodes');
        const alertManager = window.app?.getManager('alerts');
        
        if (!nodeManager || !alertManager) {
            console.error('❌ 管理器不可用');
            return;
        }

        const report = {
            reportTime: new Date().toISOString(),
            systemStatus: {
                totalNodes: nodeManager.getAllNodes().length,
                onlineNodes: nodeManager.getOnlineNodesCount(),
                offlineNodes: nodeManager.getOfflineNodesCount()
            },
            nodeDetails: nodeManager.getAllNodes().map(node => ({
                id: node.id,
                name: node.name,
                status: node.status ? '在线' : '离线',
                lastUpdate: node.lastUpdate,
                currentData: node.currentData,
                alertLevel: node.alertLevel
            })),
            alertSummary: alertManager.getStatistics(),
            activeAlerts: alertManager.getActiveAlerts().map(alert => ({
                nodeId: alert.nodeId,
                type: alert.type,
                level: alert.level,
                message: alert.message,
                timestamp: alert.timestamp
            }))
        };

        const filename = `node_status_report_${this.getTimestamp()}.json`;
        const jsonString = JSON.stringify(report, null, 2);
        this.downloadFile(jsonString, filename, 'application/json');
        
        console.log(`📋 节点状态报告已导出: ${filename}`);
    }

    // 导出系统配置
    exportSystemConfig() {
        const config = {
            exportTime: new Date().toISOString(),
            sensorLimits: CONFIG.sensorLimits,
            chartConfig: {
                maxDataPoints: CONFIG.charts.maxDataPoints,
                updateInterval: CONFIG.charts.updateInterval,
                yAxisRanges: CONFIG.charts.yAxisRanges
            },
            nodeConfig: {
                count: CONFIG.nodes.count,
                names: CONFIG.nodes.names,
                updateInterval: CONFIG.nodes.updateInterval,
                offlineTimeout: CONFIG.nodes.offlineTimeout
            },
            websocketConfig: {
                url: CONFIG.websocket.url,
                options: CONFIG.websocket.options
            }
        };

        const filename = `system_config_${this.getTimestamp()}.json`;
        const jsonString = JSON.stringify(config, null, 2);
        this.downloadFile(jsonString, filename, 'application/json');
        
        console.log(`⚙️ 系统配置已导出: ${filename}`);
    }

    // 下载文件
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理URL对象
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 100);
    }

    // 获取时间戳
    getTimestamp() {
        return new Date().toISOString()
            .replace(/[:.]/g, '-')
            .replace('T', '_')
            .split('.')[0];
    }

    // 获取支持的格式
    getSupportedFormats() {
        return [...this.supportedFormats];
    }

    // 批量导出
    batchExport(formats = ['json', 'csv']) {
        formats.forEach(format => {
            if (this.supportedFormats.includes(format)) {
                this.exportChartData(format);
            }
        });
        
        console.log(`📦 批量导出完成: ${formats.join(', ')}`);
    }
}

// 导出数据导出器
window.DataExporter = DataExporter;
