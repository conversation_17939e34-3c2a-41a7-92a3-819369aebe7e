// 节点详情页面管理器
class NodeDetailManager {
    constructor() {
        this.nodeId = null;
        this.nodeData = null;
        this.chart = null;
        this.currentSensorType = 'temperature';
        this.alertFilter = 'all';
        this.websocketManager = null;
        this.dataExporter = null;
        
        // 初始化
        this.init();
    }

    // 初始化
    async init() {
        try {
            console.log('🔧 初始化节点详情页面...');
            
            // 获取URL参数中的节点ID
            this.nodeId = this.getNodeIdFromURL();
            
            if (!this.nodeId) {
                console.error('❌ 未找到节点ID');
                this.showError('节点ID无效');
                return;
            }
            
            // 显示加载屏幕
            this.showLoadingScreen();
            
            // 初始化DOM元素
            this.initDOMElements();
            
            // 初始化主题
            this.initTheme();
            
            // 初始化事件监听器
            this.initEventListeners();
            
            // 初始化WebSocket连接
            this.initWebSocket();
            
            // 初始化图表
            this.initChart();
            
            // 初始化数据导出器
            this.dataExporter = new DataExporter();
            
            // 模拟加载过程
            await this.simulateLoading();
            
            // 隐藏加载屏幕
            this.hideLoadingScreen();
            
            console.log('✅ 节点详情页面初始化完成');
            
        } catch (error) {
            console.error('❌ 节点详情页面初始化失败:', error);
            this.showError('页面初始化失败');
        }
    }

    // 从URL获取节点ID
    getNodeIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('nodeId')) || null;
    }

    // 初始化DOM元素
    initDOMElements() {
        this.loadingScreen = Utils.dom.$('#loadingScreen');
        this.appContainer = Utils.dom.$('#appContainer');
        this.currentTime = Utils.dom.$('#currentTime');
        this.connectionStatus = Utils.dom.$('#connectionStatus');
        this.themeToggle = Utils.dom.$('#themeToggle');
        this.backButton = Utils.dom.$('#backButton');
        this.pageTitle = Utils.dom.$('#pageTitle');
        
        // 节点信息元素
        this.nodeName = Utils.dom.$('#nodeName');
        this.nodeStatus = Utils.dom.$('#nodeStatus');
        this.lastUpdate = Utils.dom.$('#lastUpdate');
        this.dataPoints = Utils.dom.$('#dataPoints');
        this.alertCount = Utils.dom.$('#alertCount');
        
        // 传感器值元素
        this.sensorValues = {
            temperature: Utils.dom.$('#temperatureValue'),
            humidity: Utils.dom.$('#humidityValue'),
            light: Utils.dom.$('#lightValue'),
            smoke: Utils.dom.$('#smokeValue')
        };
        
        // 传感器状态元素
        this.sensorStatuses = {
            temperature: Utils.dom.$('#temperatureStatus'),
            humidity: Utils.dom.$('#humidityStatus'),
            light: Utils.dom.$('#lightStatus'),
            smoke: Utils.dom.$('#smokeStatus')
        };
        
        // 按钮元素
        this.resetNodeBtn = Utils.dom.$('#resetNodeBtn');
        this.configNodeBtn = Utils.dom.$('#configNodeBtn');
        this.exportDataBtn = Utils.dom.$('#exportDataBtn');
        
        // 图表和过滤器
        this.chartTabs = Utils.dom.$$('.chart-tab');
        this.filterBtns = Utils.dom.$$('.filter-btn');
        this.alertList = Utils.dom.$('#alertList');
        
        console.log('📱 DOM元素初始化完成');
    }

    // 初始化主题
    initTheme() {
        Utils.theme.init();
        console.log('🎨 主题初始化完成');
    }

    // 初始化事件监听器
    initEventListeners() {
        // 返回按钮
        if (this.backButton) {
            this.backButton.addEventListener('click', () => {
                this.goBack();
            });
        }

        // 主题切换
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                Utils.theme.toggle();
                this.updateChartTheme();
            });
        }

        // 节点操作按钮
        if (this.resetNodeBtn) {
            this.resetNodeBtn.addEventListener('click', () => {
                this.resetNode();
            });
        }

        if (this.configNodeBtn) {
            this.configNodeBtn.addEventListener('click', () => {
                this.configNode();
            });
        }

        if (this.exportDataBtn) {
            this.exportDataBtn.addEventListener('click', () => {
                this.exportNodeData();
            });
        }

        // 图表切换
        this.chartTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const sensorType = tab.dataset.chart;
                this.switchChart(sensorType);
                
                // 更新活跃状态
                this.chartTabs.forEach(t => Utils.dom.removeClass(t, 'active'));
                Utils.dom.addClass(tab, 'active');
            });
        });

        // 警告过滤器
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const level = btn.dataset.level;
                this.filterAlerts(level);
                
                // 更新活跃状态
                this.filterBtns.forEach(b => Utils.dom.removeClass(b, 'active'));
                Utils.dom.addClass(btn, 'active');
            });
        });

        // 时间更新
        this.updateTime();
        setInterval(() => {
            this.updateTime();
        }, 1000);

        console.log('👂 事件监听器初始化完成');
    }

    // 初始化WebSocket连接
    initWebSocket() {
        this.websocketManager = new WebSocketManager();
        
        // 监听连接状态
        this.websocketManager.on('connected', () => {
            this.updateConnectionStatus(true, '已连接');
            // 订阅特定节点数据
            this.websocketManager.subscribeToNode(this.nodeId);
        });
        
        this.websocketManager.on('disconnected', () => {
            this.updateConnectionStatus(false, '连接断开');
        });
        
        // 监听数据更新
        this.websocketManager.on('dataUpdate', (data) => {
            this.handleDataUpdate(data);
        });
        
        // 监听初始数据
        this.websocketManager.on('initialData', (data) => {
            this.handleInitialData(data);
        });
        
        console.log('🔗 WebSocket连接初始化完成');
    }

    // 初始化图表
    initChart() {
        const canvas = Utils.dom.$('#nodeChart');
        if (!canvas) {
            console.warn('⚠️ 图表画布不存在');
            return;
        }

        const ctx = canvas.getContext('2d');
        const sensorConfig = CONFIG.utils.getSensorConfig(this.currentSensorType);

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: `节点${this.nodeId}`,
                    data: [],
                    borderColor: ChartUtils.getSensorColorTheme(this.currentSensorType).primary,
                    backgroundColor: ChartUtils.getSensorColorTheme(this.currentSensorType).primary + '20',
                    borderWidth: 3,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    fill: true
                }]
            },
            options: {
                ...ChartUtils.createResponsiveConfig(),
                plugins: {
                    title: {
                        display: true,
                        text: `${sensorConfig.name}历史趋势`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: ChartUtils.createTooltipConfig(this.currentSensorType)
                },
                scales: ChartUtils.createScalesConfig(this.currentSensorType),
                animation: ChartUtils.createAnimationConfig('default')
            }
        });

        console.log('📊 图表初始化完成');
    }

    // 处理数据更新
    handleDataUpdate(data) {
        if (!data.nodes || !Array.isArray(data.nodes)) return;

        const nodeData = data.nodes.find(node => node.id === this.nodeId);
        if (!nodeData) return;

        this.nodeData = nodeData;
        this.updateNodeDisplay();
        this.updateChart(nodeData.currentData);
    }

    // 处理初始数据
    handleInitialData(data) {
        if (!data.nodes || !Array.isArray(data.nodes)) return;

        const nodeData = data.nodes.find(node => node.id === this.nodeId);
        if (!nodeData) return;

        this.nodeData = nodeData;
        this.updateNodeDisplay();
    }

    // 更新节点显示
    updateNodeDisplay() {
        if (!this.nodeData) return;

        // 更新节点名称
        if (this.nodeName) {
            this.nodeName.textContent = this.nodeData.name || `节点${this.nodeId}`;
        }

        // 更新页面标题
        if (this.pageTitle) {
            this.pageTitle.textContent = `${this.nodeData.name || `节点${this.nodeId}`} - 详情`;
        }

        // 更新节点状态
        if (this.nodeStatus) {
            const statusText = this.nodeStatus.querySelector('.status-text');
            const statusDot = this.nodeStatus.querySelector('.status-dot');
            
            if (statusText && statusDot) {
                statusText.textContent = this.nodeData.status ? '在线' : '离线';
                Utils.dom.removeClass(this.nodeStatus, 'online', 'offline');
                Utils.dom.addClass(this.nodeStatus, this.nodeData.status ? 'online' : 'offline');
            }
        }

        // 更新统计信息
        if (this.lastUpdate) {
            this.lastUpdate.textContent = this.nodeData.lastUpdate 
                ? Utils.formatRelativeTime(this.nodeData.lastUpdate)
                : '从未';
        }

        // 更新传感器数据
        if (this.nodeData.currentData) {
            Object.keys(this.nodeData.currentData).forEach(sensorType => {
                const value = this.nodeData.currentData[sensorType];
                const formattedValue = Utils.formatSensorValue(sensorType, value);
                
                // 更新数值显示
                if (this.sensorValues[sensorType]) {
                    this.sensorValues[sensorType].textContent = formattedValue;
                }
                
                // 更新状态显示
                if (this.sensorStatuses[sensorType]) {
                    const isValid = Utils.validateSensorValue(sensorType, value);
                    const statusElement = this.sensorStatuses[sensorType];
                    
                    Utils.dom.removeClass(statusElement, 'normal', 'warning', 'error');
                    
                    if (isValid) {
                        Utils.dom.addClass(statusElement, 'normal');
                        statusElement.textContent = '正常';
                    } else {
                        Utils.dom.addClass(statusElement, 'error');
                        statusElement.textContent = '异常';
                    }
                }
            });
        }
    }

    // 更新图表
    updateChart(sensorData) {
        if (!this.chart || !sensorData) return;

        const timestamp = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        const value = sensorData[this.currentSensorType];
        if (value !== undefined) {
            // 添加新数据点
            this.chart.data.labels.push(timestamp);
            this.chart.data.datasets[0].data.push(value);

            // 限制数据点数量
            const maxPoints = 50;
            if (this.chart.data.labels.length > maxPoints) {
                this.chart.data.labels.shift();
                this.chart.data.datasets[0].data.shift();
            }

            this.chart.update('none');
        }
    }

    // 切换图表
    switchChart(sensorType) {
        this.currentSensorType = sensorType;
        
        if (this.chart) {
            const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
            const theme = ChartUtils.getSensorColorTheme(sensorType);
            
            // 更新图表配置
            this.chart.options.plugins.title.text = `${sensorConfig.name}历史趋势`;
            this.chart.data.datasets[0].borderColor = theme.primary;
            this.chart.data.datasets[0].backgroundColor = theme.primary + '20';
            this.chart.options.scales = ChartUtils.createScalesConfig(sensorType);
            
            // 清空数据重新开始
            this.chart.data.labels = [];
            this.chart.data.datasets[0].data = [];
            
            this.chart.update();
        }
        
        console.log(`📊 切换到${sensorType}图表`);
    }

    // 过滤警告
    filterAlerts(level) {
        this.alertFilter = level;
        this.updateAlertList();
    }

    // 更新警告列表
    updateAlertList() {
        if (!this.alertList) return;

        // 这里应该从警告管理器获取数据
        // 暂时显示示例数据
        this.alertList.innerHTML = `
            <div class="alert-item">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content">
                    <div class="alert-title">温度异常</div>
                    <div class="alert-message">温度值45°C超出正常范围</div>
                    <div class="alert-time">2分钟前</div>
                </div>
            </div>
        `;
    }

    // 节点操作方法
    resetNode() {
        if (confirm(`确定要重置节点${this.nodeId}吗？`)) {
            console.log(`🔄 重置节点${this.nodeId}`);
            // 这里应该发送重置命令到后端
        }
    }

    configNode() {
        console.log(`⚙️ 配置节点${this.nodeId}`);
        // 这里应该打开配置对话框
        alert('配置功能开发中...');
    }

    exportNodeData() {
        if (this.dataExporter) {
            this.dataExporter.exportChartData('json', this.currentSensorType);
            console.log(`📄 导出节点${this.nodeId}数据`);
        }
    }

    // 返回主页
    goBack() {
        window.location.href = '../index.html';
    }

    // 更新连接状态
    updateConnectionStatus(isConnected, statusText) {
        if (this.connectionStatus) {
            const statusDot = this.connectionStatus.querySelector('.status-dot');
            const statusTextElement = this.connectionStatus.querySelector('.status-text');
            
            if (statusDot) {
                Utils.dom.removeClass(statusDot, 'connected', 'disconnected');
                Utils.dom.addClass(statusDot, isConnected ? 'connected' : 'disconnected');
            }
            
            if (statusTextElement) {
                statusTextElement.textContent = statusText;
            }
        }
    }

    // 更新时间显示
    updateTime() {
        if (this.currentTime) {
            this.currentTime.textContent = Utils.formatTime();
        }
    }

    // 更新图表主题
    updateChartTheme() {
        if (this.chart) {
            // 重新应用主题配置
            this.chart.options.scales = ChartUtils.createScalesConfig(this.currentSensorType);
            this.chart.update();
        }
    }

    // 显示加载屏幕
    showLoadingScreen() {
        if (this.loadingScreen) {
            this.loadingScreen.style.display = 'flex';
            Utils.dom.removeClass(this.loadingScreen, 'hidden');
        }
        
        if (this.appContainer) {
            this.appContainer.style.display = 'none';
        }
    }

    // 隐藏加载屏幕
    hideLoadingScreen() {
        if (this.loadingScreen) {
            Utils.dom.addClass(this.loadingScreen, 'hidden');
            
            setTimeout(() => {
                this.loadingScreen.style.display = 'none';
            }, 500);
        }
        
        if (this.appContainer) {
            this.appContainer.style.display = 'flex';
            Utils.animation.fadeIn(this.appContainer, 500);
        }
    }

    // 模拟加载过程
    async simulateLoading() {
        const loadingText = Utils.dom.$('.loading-content h2');
        const steps = [
            '加载节点详情...',
            '连接节点...',
            '获取历史数据...',
            '初始化图表...',
            '准备就绪！'
        ];

        for (let i = 0; i < steps.length; i++) {
            if (loadingText) {
                loadingText.textContent = steps[i];
            }
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    }

    // 显示错误信息
    showError(message) {
        console.error('❌ 错误:', message);
        alert(message);
    }
}

// 当DOM加载完成后初始化节点详情管理器
document.addEventListener('DOMContentLoaded', () => {
    window.nodeDetailManager = new NodeDetailManager();
});
