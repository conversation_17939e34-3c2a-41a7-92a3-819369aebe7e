// 节点配置管理器 - 复用Qt项目的配置逻辑
class NodeConfigManager {
    constructor() {
        this.nodeConfigs = new Map(); // 存储节点配置
        this.defaultConfig = this.getDefaultConfig();
        
        // 初始化
        this.init();
    }

    // 初始化配置管理器
    init() {
        // 为每个节点初始化默认配置
        for (let i = 1; i <= CONFIG.nodes.count; i++) {
            this.nodeConfigs.set(i, { ...this.defaultConfig });
        }
        
        // 从本地存储加载配置
        this.loadConfigsFromStorage();
        
        console.log('⚙️ 节点配置管理器初始化完成');
    }

    // 获取默认配置
    getDefaultConfig() {
        return {
            // 传感器配置
            sensors: {
                temperature: {
                    enabled: true,
                    sampleRate: 50, // ms
                    calibration: 0, // 校准偏移
                    smoothing: true // 数据平滑
                },
                humidity: {
                    enabled: true,
                    sampleRate: 50,
                    calibration: 0,
                    smoothing: true
                },
                light: {
                    enabled: true,
                    sampleRate: 50,
                    calibration: 0,
                    smoothing: true
                },
                smoke: {
                    enabled: true,
                    sampleRate: 50,
                    calibration: 0,
                    smoothing: true
                }
            },
            
            // 警告配置
            alerts: {
                enabled: true,
                thresholds: {
                    temperature: { min: 0, max: 39 },
                    humidity: { min: 10, max: 99 },
                    light: { min: 10, max: 800 },
                    smoke: { min: 0, max: 600 }
                },
                notifications: {
                    sound: true,
                    popup: true,
                    email: false
                }
            },
            
            // 通信配置
            communication: {
                protocol: 'mqtt',
                heartbeatInterval: 30000, // 30秒
                reconnectAttempts: 5,
                timeout: 5000
            },
            
            // 数据存储配置
            dataStorage: {
                enabled: true,
                maxPoints: 1000,
                autoCleanup: true,
                exportFormat: 'json'
            },
            
            // 显示配置
            display: {
                updateInterval: 50,
                showTrends: true,
                showAlerts: true,
                theme: 'auto'
            }
        };
    }

    // 获取节点配置
    getNodeConfig(nodeId) {
        return this.nodeConfigs.get(nodeId) || { ...this.defaultConfig };
    }

    // 更新节点配置
    updateNodeConfig(nodeId, newConfig) {
        const currentConfig = this.getNodeConfig(nodeId);
        const updatedConfig = this.mergeConfigs(currentConfig, newConfig);
        
        this.nodeConfigs.set(nodeId, updatedConfig);
        this.saveConfigsToStorage();
        
        console.log(`⚙️ 节点${nodeId}配置已更新:`, updatedConfig);
        
        // 触发配置更新事件
        this.triggerConfigUpdateEvent(nodeId, updatedConfig);
        
        return updatedConfig;
    }

    // 合并配置
    mergeConfigs(currentConfig, newConfig) {
        const merged = { ...currentConfig };
        
        Object.keys(newConfig).forEach(key => {
            if (typeof newConfig[key] === 'object' && !Array.isArray(newConfig[key])) {
                merged[key] = { ...merged[key], ...newConfig[key] };
            } else {
                merged[key] = newConfig[key];
            }
        });
        
        return merged;
    }

    // 重置节点配置
    resetNodeConfig(nodeId) {
        this.nodeConfigs.set(nodeId, { ...this.defaultConfig });
        this.saveConfigsToStorage();
        
        console.log(`🔄 节点${nodeId}配置已重置`);
        
        // 触发配置重置事件
        this.triggerConfigUpdateEvent(nodeId, this.defaultConfig);
        
        return this.defaultConfig;
    }

    // 导出节点配置
    exportNodeConfig(nodeId) {
        const config = this.getNodeConfig(nodeId);
        const exportData = {
            nodeId,
            exportTime: new Date().toISOString(),
            config
        };
        
        const filename = `node_${nodeId}_config_${this.getTimestamp()}.json`;
        const jsonString = JSON.stringify(exportData, null, 2);
        
        this.downloadFile(jsonString, filename, 'application/json');
        
        console.log(`📄 节点${nodeId}配置已导出: ${filename}`);
    }

    // 导入节点配置
    importNodeConfig(nodeId, configFile) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const importData = JSON.parse(e.target.result);
                    
                    if (importData.config) {
                        const updatedConfig = this.updateNodeConfig(nodeId, importData.config);
                        console.log(`📥 节点${nodeId}配置已导入`);
                        resolve(updatedConfig);
                    } else {
                        reject(new Error('配置文件格式无效'));
                    }
                } catch (error) {
                    reject(new Error('配置文件解析失败: ' + error.message));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };
            
            reader.readAsText(configFile);
        });
    }

    // 验证配置
    validateConfig(config) {
        const errors = [];
        
        // 验证传感器配置
        if (config.sensors) {
            Object.keys(config.sensors).forEach(sensorType => {
                const sensorConfig = config.sensors[sensorType];
                
                if (sensorConfig.sampleRate < 10 || sensorConfig.sampleRate > 10000) {
                    errors.push(`${sensorType}采样率必须在10-10000ms之间`);
                }
                
                if (Math.abs(sensorConfig.calibration) > 100) {
                    errors.push(`${sensorType}校准值不能超过±100`);
                }
            });
        }
        
        // 验证警告配置
        if (config.alerts && config.alerts.thresholds) {
            Object.keys(config.alerts.thresholds).forEach(sensorType => {
                const threshold = config.alerts.thresholds[sensorType];
                
                if (threshold.min >= threshold.max) {
                    errors.push(`${sensorType}最小阈值必须小于最大阈值`);
                }
            });
        }
        
        // 验证通信配置
        if (config.communication) {
            if (config.communication.heartbeatInterval < 1000) {
                errors.push('心跳间隔不能小于1秒');
            }
            
            if (config.communication.reconnectAttempts < 1 || config.communication.reconnectAttempts > 10) {
                errors.push('重连次数必须在1-10之间');
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 应用配置到节点
    applyConfigToNode(nodeId) {
        const config = this.getNodeConfig(nodeId);
        const nodeManager = window.app?.getManager('nodes');
        
        if (nodeManager) {
            // 更新节点的更新间隔
            nodeManager.updateInterval = config.display.updateInterval;
            
            console.log(`✅ 配置已应用到节点${nodeId}`);
        }
        
        // 发送配置到后端
        this.sendConfigToBackend(nodeId, config);
    }

    // 发送配置到后端
    sendConfigToBackend(nodeId, config) {
        const websocketManager = window.app?.getManager('websocket');
        
        if (websocketManager && websocketManager.isConnected) {
            websocketManager.send('node-config-update', {
                nodeId,
                config
            });
            
            console.log(`📡 节点${nodeId}配置已发送到后端`);
        }
    }

    // 从本地存储加载配置
    loadConfigsFromStorage() {
        try {
            const savedConfigs = Utils.storage.get('nodeConfigs');
            
            if (savedConfigs) {
                Object.keys(savedConfigs).forEach(nodeId => {
                    this.nodeConfigs.set(parseInt(nodeId), savedConfigs[nodeId]);
                });
                
                console.log('📥 节点配置已从本地存储加载');
            }
        } catch (error) {
            console.error('❌ 加载节点配置失败:', error);
        }
    }

    // 保存配置到本地存储
    saveConfigsToStorage() {
        try {
            const configsToSave = {};
            
            this.nodeConfigs.forEach((config, nodeId) => {
                configsToSave[nodeId] = config;
            });
            
            Utils.storage.set('nodeConfigs', configsToSave);
            console.log('💾 节点配置已保存到本地存储');
        } catch (error) {
            console.error('❌ 保存节点配置失败:', error);
        }
    }

    // 触发配置更新事件
    triggerConfigUpdateEvent(nodeId, config) {
        const event = new CustomEvent('nodeConfigUpdated', {
            detail: { nodeId, config }
        });
        document.dispatchEvent(event);
    }

    // 获取所有节点配置
    getAllConfigs() {
        const allConfigs = {};
        
        this.nodeConfigs.forEach((config, nodeId) => {
            allConfigs[nodeId] = config;
        });
        
        return allConfigs;
    }

    // 批量更新配置
    batchUpdateConfigs(configUpdates) {
        const results = {};
        
        Object.keys(configUpdates).forEach(nodeId => {
            try {
                const updatedConfig = this.updateNodeConfig(parseInt(nodeId), configUpdates[nodeId]);
                results[nodeId] = { success: true, config: updatedConfig };
            } catch (error) {
                results[nodeId] = { success: false, error: error.message };
            }
        });
        
        return results;
    }

    // 下载文件
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 100);
    }

    // 获取时间戳
    getTimestamp() {
        return new Date().toISOString()
            .replace(/[:.]/g, '-')
            .replace('T', '_')
            .split('.')[0];
    }
}

// 导出节点配置管理器
window.NodeConfigManager = NodeConfigManager;
