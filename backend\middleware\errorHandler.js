// 错误处理中间件
const ResponseHandler = require('./responseHandler');

// 全局错误处理中间件
const errorHandler = (err, req, res, next) => {
    console.error('Error occurred:', {
        message: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        timestamp: new Date().toISOString()
    });

    // 处理不同类型的错误
    if (err.name === 'ValidationError') {
        return ResponseHandler.validationError(res, err.message);
    }

    if (err.name === 'CastError') {
        return ResponseHandler.validationError(res, 'Invalid data format');
    }

    if (err.code === 'ENOENT') {
        return ResponseHandler.notFound(res, 'File');
    }

    // 默认服务器错误
    return ResponseHandler.error(res, 'Internal server error', 500);
};

// 404处理中间件
const notFoundHandler = (req, res) => {
    return ResponseHandler.notFound(res, 'API endpoint');
};

// 异步错误包装器
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};

module.exports = {
    errorHandler,
    notFoundHandler,
    asyncHandler
};
