// 节点信息模型 - 复用Qt项目节点管理逻辑
class NodeInfo {
    constructor(id, name = null) {
        this.id = id; // 节点ID (1, 2, 3)
        this.name = name || `节点${this.id}`; // 节点名称
        this.status = false; // 在线状态 (复用Qt的flag1-3)
        this.lastSeen = null; // 最后活跃时间
        this.alertLevel = 'normal'; // 警告级别: normal, warning, error
        this.currentData = null; // 当前传感器数据
        this.dataHistory = []; // 历史数据缓存
        this.alertHistory = []; // 警告历史
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    // 创建节点实例
    static create(id, name = null) {
        if (!id || ![1, 2, 3].includes(Number(id))) {
            throw new Error('Invalid node ID. Must be 1, 2, or 3');
        }
        return new NodeInfo(Number(id), name);
    }

    // 更新节点状态
    updateStatus(isOnline) {
        this.status = isOnline;
        this.updatedAt = new Date();
        
        if (isOnline) {
            this.lastSeen = new Date();
        }
    }

    // 更新传感器数据
    updateSensorData(sensorData) {
        this.currentData = sensorData;
        this.lastSeen = new Date();
        this.updatedAt = new Date();
        
        // 检查是否在线 - 复用Qt逻辑：所有数据为0视为离线
        const isOnline = !(
            sensorData.temperature === 0 && 
            sensorData.humidity === 0 && 
            sensorData.light === 0 && 
            sensorData.smoke === 0
        );
        
        this.updateStatus(isOnline);
        
        // 添加到历史数据
        this.addToHistory(sensorData);
        
        // 检查警告
        const alerts = sensorData.checkAlerts();
        if (alerts.length > 0) {
            this.updateAlertLevel(alerts);
            this.addAlerts(alerts);
        } else {
            this.alertLevel = 'normal';
        }
    }

    // 添加历史数据
    addToHistory(sensorData) {
        this.dataHistory.push(sensorData);
        
        // 限制历史数据数量，保留最近100条
        if (this.dataHistory.length > 100) {
            this.dataHistory = this.dataHistory.slice(-100);
        }
    }

    // 更新警告级别
    updateAlertLevel(alerts) {
        const hasError = alerts.some(alert => alert.level === 'error');
        const hasWarning = alerts.some(alert => alert.level === 'warning');
        
        if (hasError) {
            this.alertLevel = 'error';
        } else if (hasWarning) {
            this.alertLevel = 'warning';
        } else {
            this.alertLevel = 'normal';
        }
    }

    // 添加警告记录
    addAlerts(alerts) {
        const timestamp = new Date();
        alerts.forEach(alert => {
            this.alertHistory.push({
                ...alert,
                nodeId: this.id,
                timestamp
            });
        });
        
        // 限制警告历史数量
        if (this.alertHistory.length > 50) {
            this.alertHistory = this.alertHistory.slice(-50);
        }
    }

    // 检查节点是否离线
    isOffline(timeoutMs = 5000) {
        if (!this.lastSeen) return true;
        return (new Date() - this.lastSeen) > timeoutMs;
    }

    // 获取历史数据
    getHistory(hours = 1) {
        const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
        return this.dataHistory.filter(data => 
            new Date(data.timestamp) > cutoffTime
        );
    }

    // 获取警告历史
    getAlertHistory(hours = 24) {
        const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
        return this.alertHistory.filter(alert => 
            new Date(alert.timestamp) > cutoffTime
        );
    }

    // 转换为JSON格式
    toJSON() {
        return {
            id: this.id,
            name: this.name,
            status: this.status,
            lastSeen: this.lastSeen ? this.lastSeen.toISOString() : null,
            alertLevel: this.alertLevel,
            currentData: this.currentData ? this.currentData.toJSON() : null,
            dataCount: this.dataHistory.length,
            alertCount: this.alertHistory.length,
            isOffline: this.isOffline(),
            createdAt: this.createdAt.toISOString(),
            updatedAt: this.updatedAt.toISOString()
        };
    }

    // 获取简要信息
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            status: this.status,
            alertLevel: this.alertLevel,
            lastSeen: this.lastSeen ? this.lastSeen.toISOString() : null,
            isOffline: this.isOffline()
        };
    }

    // 重置节点数据
    reset() {
        this.status = false;
        this.lastSeen = null;
        this.alertLevel = 'normal';
        this.currentData = null;
        this.dataHistory = [];
        this.alertHistory = [];
        this.updatedAt = new Date();
    }
}

module.exports = NodeInfo;
