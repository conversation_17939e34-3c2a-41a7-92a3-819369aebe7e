{"name": "mqtt", "description": "A library for the MQTT protocol", "version": "5.13.1", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/scarry1992)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/YoDaMa)", "<PERSON> <<EMAIL>> (https://github.com/robertsLando)"], "keywords": ["mqtt", "publish/subscribe", "publish", "subscribe"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/mqttjs/MQTT.js.git"}, "main": "./build/index.js", "module": "./dist/mqtt.esm.js", "bin": {"mqtt_pub": "./build/bin/pub.js", "mqtt_sub": "./build/bin/sub.js", "mqtt": "./build/bin/mqtt.js"}, "files": ["dist/", "CONTRIBUTING.md", "LICENSE.md", "help/", "build/", "src/"], "exports": {".": {"react-native": "./dist/mqtt.esm.js", "browser": {"import": "./dist/mqtt.esm.js", "default": "./dist/mqtt.min.js"}, "default": "./build/index.js"}, "./package.json": "./package.json", "./*.map": "./build/*.js.map", "./dist/*": "./dist/*.js", "./*": "./build/*.js"}, "types": "build/index.d.ts", "typesVersions": {"*": {"*": ["./build/index.d.ts"]}}, "scripts": {"lint": "eslint --ext .ts .", "lint-fix": "eslint --fix --ext .ts .", "build:ts": "rimraf build/ && tsc -p tsconfig.build.json", "build:browser": "node esbuild.js", "build": "npm run build:ts && npm run build:browser", "prepare": "npm run build", "unit-test:node": "node -r esbuild-register --test-concurrency 4 --test-reporter=junit --test-reporter-destination=junit.xml --test-reporter=spec --test-reporter-destination=stdout --test test/node/*.ts ", "unit-test:browser": "wtr", "test:node": "node_modules/.bin/nyc npm run unit-test:node", "test:browser": "npm run build && npm run unit-test:browser", "test": "npm run test:node", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "changelog-init": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "release": "read -p 'GITHUB_TOKEN: ' GITHUB_TOKEN && export GITHUB_TOKEN=$GITHUB_TOKEN && release-it"}, "release-it": {"github": {"release": true}, "git": {"tagName": "v${version}", "commitMessage": "chore(release): ${version}"}, "hooks": {"before:init": ["npm run test"]}, "npm": {"publish": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "publishConfig": {"provenance": true}, "pre-commit": ["lint"], "engines": {"node": ">=16.0.0"}, "browser": {"./mqtt.js": "./dist/mqtt.js", "fs": false, "tls": false, "net": false}, "dependencies": {"commist": "^3.2.0", "concat-stream": "^2.0.0", "debug": "^4.4.0", "help-me": "^5.0.0", "lru-cache": "^10.4.3", "minimist": "^1.2.8", "mqtt-packet": "^9.0.2", "number-allocator": "^1.0.14", "readable-stream": "^4.7.0", "rfdc": "^1.4.1", "socks": "^2.8.3", "split2": "^4.2.0", "worker-timers": "^7.1.8", "ws": "^8.18.0"}, "devDependencies": {"@esm-bundle/chai": "^4.3.4", "@release-it/conventional-changelog": "^7.0.2", "@types/chai": "^4.3.20", "@types/node": "^20.17.16", "@types/readable-stream": "^4.0.18", "@types/sinon": "^17.0.3", "@types/tape": "^5.8.1", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@web/test-runner": "^0.19.0", "@web/test-runner-playwright": "^0.11.0", "aedes-cli": "^0.8.0", "chai": "^4.5.0", "chokidar": "^3.6.0", "conventional-changelog-cli": "^4.1.0", "end-of-stream": "^1.4.4", "esbuild": "^0.25.0", "esbuild-plugin-polyfill-node": "^0.3.0", "esbuild-register": "^3.6.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "global": "^4.4.0", "leaked-handles": "^5.2.0", "mkdirp": "^3.0.1", "mqtt-connection": "^4.1.0", "mqtt-level-store": "^3.1.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "prettier": "^3.4.2", "release-it": "^16.3.0", "rimraf": "^5.0.10", "should": "^13.2.3", "sinon": "^17.0.2", "snazzy": "^9.0.0", "tape": "^5.9.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}