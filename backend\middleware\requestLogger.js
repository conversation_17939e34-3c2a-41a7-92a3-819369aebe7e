// 请求日志中间件
const requestLogger = (req, res, next) => {
    const startTime = Date.now();
    
    // 记录请求开始
    console.log(`📥 ${req.method} ${req.url} - ${new Date().toISOString()}`);
    
    // 监听响应完成
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const statusColor = res.statusCode >= 400 ? '🔴' : '🟢';
        
        console.log(`📤 ${statusColor} ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    });
    
    next();
};

module.exports = requestLogger;
