// 图表管理器 - 复用Qt Charts设置
class ChartManager {
    constructor() {
        this.charts = new Map(); // 存储图表实例
        this.chartData = new Map(); // 存储图表数据
        this.maxDataPoints = CONFIG.charts.maxDataPoints; // 最大数据点数
        this.updateInterval = CONFIG.charts.updateInterval; // 更新间隔
        this.colors = CONFIG.charts.colors; // 图表颜色
        this.yAxisRanges = CONFIG.charts.yAxisRanges; // Y轴范围
        
        // 初始化
        this.init();
    }

    // 初始化图表管理器
    init() {
        // 检查Chart.js是否可用
        if (typeof Chart === 'undefined') {
            console.warn('⚠️ Chart.js未加载，图表功能不可用');
            return;
        }

        // 初始化图表数据
        this.initChartData();

        // 创建图表
        this.createCharts();

        // 初始化图表控制器
        this.initChartControls();

        console.log('📊 图表管理器初始化完成');
    }

    // 初始化图表数据
    initChartData() {
        const sensorTypes = ['temperature', 'humidity', 'light', 'smoke'];
        
        sensorTypes.forEach(type => {
            this.chartData.set(type, {
                labels: [],
                datasets: [
                    {
                        label: '节点1',
                        data: [],
                        borderColor: this.colors[type],
                        backgroundColor: this.colors[type] + '20',
                        tension: 0.4,
                        pointRadius: 2,
                        pointHoverRadius: 4
                    },
                    {
                        label: '节点2', 
                        data: [],
                        borderColor: this.adjustColor(this.colors[type], 0.3),
                        backgroundColor: this.adjustColor(this.colors[type], 0.3) + '20',
                        tension: 0.4,
                        pointRadius: 2,
                        pointHoverRadius: 4
                    },
                    {
                        label: '节点3',
                        data: [],
                        borderColor: this.adjustColor(this.colors[type], 0.6),
                        backgroundColor: this.adjustColor(this.colors[type], 0.6) + '20',
                        tension: 0.4,
                        pointRadius: 2,
                        pointHoverRadius: 4
                    }
                ]
            });
        });
    }

    // 创建图表
    createCharts() {
        const sensorTypes = ['temperature', 'humidity', 'light', 'smoke'];
        
        sensorTypes.forEach(type => {
            this.createChart(type);
        });
    }

    // 创建单个图表
    createChart(sensorType) {
        const canvasId = `${sensorType}Chart`;
        const canvas = Utils.dom.$(`#${canvasId}`);

        if (!canvas) {
            console.warn(`⚠️ 图表画布 ${canvasId} 不存在`);
            return;
        }

        const ctx = canvas.getContext('2d');
        const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);

        // 使用ChartUtils创建增强配置
        const chartConfig = {
            type: 'line',
            data: this.chartData.get(sensorType),
            options: {
                ...ChartUtils.createResponsiveConfig(),
                plugins: {
                    title: {
                        display: true,
                        text: `${sensorConfig.name}实时监控`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: getComputedStyle(document.documentElement)
                            .getPropertyValue('--text-primary').trim()
                    },
                    legend: ChartUtils.createLegendConfig(),
                    tooltip: ChartUtils.createTooltipConfig(sensorType)
                },
                scales: ChartUtils.createScalesConfig(sensorType),
                animation: ChartUtils.createAnimationConfig('default')
            }
        };

        // 添加警告区域插件
        const alertZonePlugin = ChartUtils.createAlertZonePlugin(sensorType);
        if (alertZonePlugin) {
            chartConfig.plugins = [alertZonePlugin];
        }

        const chart = new Chart(ctx, chartConfig);
        this.charts.set(sensorType, chart);
        console.log(`📊 ${sensorType}图表创建完成`);
    }

    // 更新图表数据
    updateChartData(nodeId, sensorData) {
        const timestamp = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        Object.keys(sensorData).forEach(sensorType => {
            const chartData = this.chartData.get(sensorType);
            const chart = this.charts.get(sensorType);
            
            if (!chartData || !chart) return;

            // 添加时间标签（只在第一个传感器类型时添加）
            if (sensorType === 'temperature') {
                chartData.labels.push(timestamp);
                
                // 限制数据点数量
                if (chartData.labels.length > this.maxDataPoints) {
                    chartData.labels.shift();
                }
            }

            // 更新对应节点的数据
            const datasetIndex = nodeId - 1; // 节点ID从1开始，数组索引从0开始
            if (chartData.datasets[datasetIndex]) {
                chartData.datasets[datasetIndex].data.push(sensorData[sensorType]);
                
                // 限制数据点数量
                if (chartData.datasets[datasetIndex].data.length > this.maxDataPoints) {
                    chartData.datasets[datasetIndex].data.shift();
                }
            }

            // 更新图表
            chart.update('none'); // 使用'none'模式避免动画延迟
        });
    }

    // 调整颜色亮度
    adjustColor(color, factor) {
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        
        const newR = Math.round(r + (255 - r) * factor);
        const newG = Math.round(g + (255 - g) * factor);
        const newB = Math.round(b + (255 - b) * factor);
        
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }

    // 清空图表数据
    clearChartData(sensorType = null) {
        if (sensorType) {
            const chartData = this.chartData.get(sensorType);
            const chart = this.charts.get(sensorType);
            
            if (chartData && chart) {
                chartData.labels = [];
                chartData.datasets.forEach(dataset => {
                    dataset.data = [];
                });
                chart.update();
            }
        } else {
            this.chartData.forEach((chartData, type) => {
                const chart = this.charts.get(type);
                if (chart) {
                    chartData.labels = [];
                    chartData.datasets.forEach(dataset => {
                        dataset.data = [];
                    });
                    chart.update();
                }
            });
        }
        
        console.log(`🧹 图表数据已清空: ${sensorType || '全部'}`);
    }

    // 获取图表实例
    getChart(sensorType) {
        return this.charts.get(sensorType);
    }

    // 初始化图表控制器
    initChartControls() {
        const chartTabs = Utils.dom.$$('.chart-tab');

        chartTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const chartType = tab.dataset.chart;
                this.switchChart(chartType);

                // 更新活跃状态
                chartTabs.forEach(t => Utils.dom.removeClass(t, 'active'));
                Utils.dom.addClass(tab, 'active');
            });
        });

        console.log('🎛️ 图表控制器初始化完成');
    }

    // 切换图表显示
    switchChart(chartType) {
        const containers = Utils.dom.$$('.chart-container');

        if (chartType === 'all') {
            // 显示所有图表的网格布局
            this.showAllCharts();
        } else {
            // 显示单个图表
            containers.forEach(container => {
                Utils.dom.removeClass(container, 'active');
            });

            const targetContainer = Utils.dom.$(`#${chartType}Container`);
            if (targetContainer) {
                Utils.dom.addClass(targetContainer, 'active');
            }
        }

        console.log(`📊 切换到${chartType}图表`);
    }

    // 显示所有图表
    showAllCharts() {
        const chartsGrid = Utils.dom.$('.charts-grid');
        if (!chartsGrid) return;

        // 创建网格布局
        chartsGrid.style.display = 'grid';
        chartsGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        chartsGrid.style.gridGap = 'var(--spacing-md)';
        chartsGrid.style.height = 'auto';

        // 显示所有容器
        const containers = Utils.dom.$$('.chart-container');
        containers.forEach(container => {
            container.style.position = 'relative';
            container.style.height = '300px';
            Utils.dom.addClass(container, 'active');
        });

        // 调整图表大小
        setTimeout(() => {
            this.charts.forEach(chart => {
                chart.resize();
            });
        }, 300);
    }

    // 重置图表布局
    resetChartLayout() {
        const chartsGrid = Utils.dom.$('.charts-grid');
        if (!chartsGrid) return;

        // 重置为单图表布局
        chartsGrid.style.display = 'block';
        chartsGrid.style.height = '400px';

        const containers = Utils.dom.$$('.chart-container');
        containers.forEach(container => {
            container.style.position = 'absolute';
            container.style.height = '100%';
            Utils.dom.removeClass(container, 'active');
        });
    }

    // 更新图表主题
    updateTheme() {
        this.charts.forEach((chart, sensorType) => {
            const textPrimary = getComputedStyle(document.documentElement)
                .getPropertyValue('--text-primary').trim();
            const textSecondary = getComputedStyle(document.documentElement)
                .getPropertyValue('--text-secondary').trim();
            const textTertiary = getComputedStyle(document.documentElement)
                .getPropertyValue('--text-tertiary').trim();

            // 更新图表选项
            if (chart.options.plugins.title) {
                chart.options.plugins.title.color = textPrimary;
            }
            if (chart.options.plugins.legend) {
                chart.options.plugins.legend.labels.color = textSecondary;
            }
            if (chart.options.scales.x) {
                chart.options.scales.x.title.color = textSecondary;
                chart.options.scales.x.grid.color = textTertiary + '40';
                chart.options.scales.x.ticks.color = textSecondary;
            }
            if (chart.options.scales.y) {
                chart.options.scales.y.title.color = textSecondary;
                chart.options.scales.y.grid.color = textTertiary + '40';
                chart.options.scales.y.ticks.color = textSecondary;
            }

            chart.update();
        });

        console.log('🎨 图表主题已更新');
    }

    // 导出图表数据
    exportChartData(sensorType = null) {
        if (sensorType) {
            const chartData = this.chartData.get(sensorType);
            return chartData ? JSON.stringify(chartData, null, 2) : null;
        } else {
            const allData = {};
            this.chartData.forEach((data, type) => {
                allData[type] = data;
            });
            return JSON.stringify(allData, null, 2);
        }
    }

    // 获取图表统计信息
    getChartStatistics() {
        const stats = {};

        this.chartData.forEach((data, sensorType) => {
            const dataPoints = data.datasets.reduce((total, dataset) => {
                return total + dataset.data.length;
            }, 0);

            stats[sensorType] = {
                dataPoints,
                timeRange: data.labels.length,
                lastUpdate: data.labels[data.labels.length - 1] || null
            };
        });

        return stats;
    }

    // 销毁所有图表
    destroyAllCharts() {
        this.charts.forEach((chart, sensorType) => {
            chart.destroy();
        });
        this.charts.clear();
        this.chartData.clear();
        console.log('🗑️ 所有图表已销毁');
    }
}

// 导出图表管理器
window.ChartManager = ChartManager;
