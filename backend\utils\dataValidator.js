// 数据验证工具 - 统一数据验证逻辑
const SensorData = require('../models/SensorData');
const NodeInfo = require('../models/NodeInfo');
const AlertConfig = require('../models/AlertConfig');

class DataValidator {
    // 验证节点ID
    static validateNodeId(nodeId) {
        const id = Number(nodeId);
        if (!Number.isInteger(id) || ![1, 2, 3].includes(id)) {
            return {
                isValid: false,
                error: 'Node ID must be 1, 2, or 3'
            };
        }
        return { isValid: true, value: id };
    }

    // 验证传感器数据
    static validateSensorData(data) {
        const required = ['temperature', 'humidity', 'light', 'smoke'];
        const errors = [];

        // 检查必需字段
        for (const field of required) {
            if (!(field in data)) {
                errors.push(`Missing required field: ${field}`);
            } else if (typeof data[field] !== 'number' || isNaN(data[field])) {
                errors.push(`${field} must be a valid number`);
            }
        }

        if (errors.length > 0) {
            return { isValid: false, errors };
        }

        // 使用SensorData模型验证范围
        return SensorData.validateRanges(data);
    }

    // 验证Qt格式数据
    static validateQtFormat(qtData) {
        if (typeof qtData !== 'string') {
            return {
                isValid: false,
                error: 'Qt data must be a string'
            };
        }

        const parts = qtData.split(',');
        if (parts.length !== 5) {
            return {
                isValid: false,
                error: 'Qt data must have 5 comma-separated values: nodeId,light,temp,humi,smoke'
            };
        }

        // 验证每个部分都是数字
        const numbers = parts.map(part => parseFloat(part.trim()));
        if (numbers.some(num => isNaN(num))) {
            return {
                isValid: false,
                error: 'All Qt data values must be valid numbers'
            };
        }

        // 验证节点ID
        const nodeIdValidation = DataValidator.validateNodeId(numbers[0]);
        if (!nodeIdValidation.isValid) {
            return nodeIdValidation;
        }

        // 验证传感器数据
        const sensorData = {
            temperature: numbers[2], // temp
            humidity: numbers[3],    // humi
            light: numbers[1],       // light
            smoke: numbers[4]        // smoke
        };

        return DataValidator.validateSensorData(sensorData);
    }

    // 验证华为云IoT格式
    static validateHuaweiIoTFormat(data) {
        if (!data || typeof data !== 'object') {
            return {
                isValid: false,
                error: 'Huawei IoT data must be an object'
            };
        }

        if (!data.services || !Array.isArray(data.services)) {
            return {
                isValid: false,
                error: 'Huawei IoT data must have services array'
            };
        }

        if (data.services.length === 0) {
            return {
                isValid: false,
                error: 'Services array cannot be empty'
            };
        }

        const service = data.services[0];
        if (!service.properties) {
            return {
                isValid: false,
                error: 'Service must have properties object'
            };
        }

        return DataValidator.validateSensorData(service.properties);
    }

    // 验证时间范围
    static validateTimeRange(startTime, endTime) {
        const start = new Date(startTime);
        const end = new Date(endTime);

        if (isNaN(start.getTime())) {
            return {
                isValid: false,
                error: 'Invalid start time format'
            };
        }

        if (isNaN(end.getTime())) {
            return {
                isValid: false,
                error: 'Invalid end time format'
            };
        }

        if (start >= end) {
            return {
                isValid: false,
                error: 'Start time must be before end time'
            };
        }

        const maxRange = 7 * 24 * 60 * 60 * 1000; // 7天
        if (end - start > maxRange) {
            return {
                isValid: false,
                error: 'Time range cannot exceed 7 days'
            };
        }

        return { isValid: true, start, end };
    }

    // 验证分页参数
    static validatePagination(page, limit) {
        const pageNum = Number(page) || 1;
        const limitNum = Number(limit) || 10;

        if (pageNum < 1) {
            return {
                isValid: false,
                error: 'Page must be greater than 0'
            };
        }

        if (limitNum < 1 || limitNum > 100) {
            return {
                isValid: false,
                error: 'Limit must be between 1 and 100'
            };
        }

        return {
            isValid: true,
            page: pageNum,
            limit: limitNum,
            offset: (pageNum - 1) * limitNum
        };
    }

    // 验证警告配置
    static validateAlertConfig(config) {
        const errors = [];

        if (config.tempRange) {
            if (typeof config.tempRange.min !== 'number' || typeof config.tempRange.max !== 'number') {
                errors.push('Temperature range min and max must be numbers');
            } else if (config.tempRange.min >= config.tempRange.max) {
                errors.push('Temperature min must be less than max');
            }
        }

        if (config.humiRange) {
            if (typeof config.humiRange.min !== 'number' || typeof config.humiRange.max !== 'number') {
                errors.push('Humidity range min and max must be numbers');
            } else if (config.humiRange.min >= config.humiRange.max) {
                errors.push('Humidity min must be less than max');
            }
        }

        if (config.lightRange) {
            if (typeof config.lightRange.min !== 'number' || typeof config.lightRange.max !== 'number') {
                errors.push('Light range min and max must be numbers');
            } else if (config.lightRange.min >= config.lightRange.max) {
                errors.push('Light min must be less than max');
            }
        }

        if (config.smokeMax !== undefined) {
            if (typeof config.smokeMax !== 'number' || config.smokeMax < 0) {
                errors.push('Smoke max must be a positive number');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 清理和标准化数据
    static sanitizeData(data) {
        const sanitized = {};
        
        // 清理传感器数据
        if (data.temperature !== undefined) {
            sanitized.temperature = Number(Number(data.temperature).toFixed(1));
        }
        if (data.humidity !== undefined) {
            sanitized.humidity = Number(Number(data.humidity).toFixed(1));
        }
        if (data.light !== undefined) {
            sanitized.light = Number(Number(data.light).toFixed(0));
        }
        if (data.smoke !== undefined) {
            sanitized.smoke = Number(Number(data.smoke).toFixed(1));
        }

        return sanitized;
    }

    // 验证API请求体
    static validateApiRequest(req, requiredFields = []) {
        const errors = [];

        // 检查Content-Type
        if (req.method === 'POST' || req.method === 'PUT') {
            if (!req.is('application/json')) {
                errors.push('Content-Type must be application/json');
            }
        }

        // 检查必需字段
        for (const field of requiredFields) {
            if (!(field in req.body)) {
                errors.push(`Missing required field: ${field}`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

module.exports = DataValidator;
