// 响应处理中间件 - 统一API响应格式
class ResponseHandler {
    // 成功响应
    static success(res, data = null, message = 'Success', statusCode = 200) {
        const response = {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString()
        };
        
        return res.status(statusCode).json(response);
    }

    // 错误响应
    static error(res, message = 'Internal Server Error', statusCode = 500, errors = null) {
        const response = {
            success: false,
            message,
            errors,
            timestamp: new Date().toISOString()
        };
        
        return res.status(statusCode).json(response);
    }

    // 分页响应
    static paginated(res, data, pagination, message = 'Success') {
        const response = {
            success: true,
            message,
            data,
            pagination: {
                page: pagination.page,
                limit: pagination.limit,
                total: pagination.total,
                totalPages: Math.ceil(pagination.total / pagination.limit)
            },
            timestamp: new Date().toISOString()
        };
        
        return res.status(200).json(response);
    }

    // 验证错误响应
    static validationError(res, errors) {
        return ResponseHandler.error(res, 'Validation failed', 400, errors);
    }

    // 未找到资源响应
    static notFound(res, resource = 'Resource') {
        return ResponseHandler.error(res, `${resource} not found`, 404);
    }

    // 未授权响应
    static unauthorized(res, message = 'Unauthorized') {
        return ResponseHandler.error(res, message, 401);
    }

    // 禁止访问响应
    static forbidden(res, message = 'Forbidden') {
        return ResponseHandler.error(res, message, 403);
    }
}

module.exports = ResponseHandler;
