// 部署脚本 - 自动化部署龙芯智能终端管理系统
const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

class DeploymentManager {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
        this.deploymentConfig = {
            port: 3000,
            host: '127.0.0.1',
            environment: 'production',
            logLevel: 'info'
        };
        this.deploymentSteps = [];
    }

    // 执行部署
    async deploy() {
        console.log('🚀 开始部署龙芯智能终端管理系统...\n');
        
        try {
            // 1. 环境检查
            await this.checkEnvironment();
            
            // 2. 依赖检查
            await this.checkDependencies();
            
            // 3. 文件完整性检查
            await this.checkFileIntegrity();
            
            // 4. 配置验证
            await this.validateConfiguration();
            
            // 5. 启动服务
            await this.startService();
            
            // 6. 健康检查
            await this.healthCheck();
            
            // 7. 生成部署报告
            this.generateDeploymentReport();
            
            console.log('🎉 部署成功完成！');
            
        } catch (error) {
            console.error('❌ 部署失败:', error.message);
            this.rollback();
            process.exit(1);
        }
    }

    // 环境检查
    async checkEnvironment() {
        console.log('🔍 检查运行环境...');
        
        // 检查Node.js版本
        const nodeVersion = process.version;
        console.log(`Node.js版本: ${nodeVersion}`);
        
        if (!this.isNodeVersionSupported(nodeVersion)) {
            throw new Error(`不支持的Node.js版本: ${nodeVersion}，需要 >= 14.0.0`);
        }
        
        // 检查操作系统
        const platform = process.platform;
        console.log(`操作系统: ${platform}`);
        
        // 检查可用内存
        const totalMemory = Math.round(require('os').totalmem() / 1024 / 1024);
        console.log(`系统内存: ${totalMemory}MB`);
        
        if (totalMemory < 512) {
            console.warn('⚠️ 系统内存较低，可能影响性能');
        }
        
        this.addDeploymentStep('环境检查', true, '运行环境检查通过');
        console.log('✅ 环境检查完成\n');
    }

    // 依赖检查
    async checkDependencies() {
        console.log('📦 检查项目依赖...');
        
        // 检查package.json
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (!fs.existsSync(packageJsonPath)) {
            throw new Error('package.json文件不存在');
        }
        
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        console.log(`项目名称: ${packageJson.name}`);
        console.log(`项目版本: ${packageJson.version}`);
        
        // 检查node_modules
        const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
        if (!fs.existsSync(nodeModulesPath)) {
            console.log('📥 安装项目依赖...');
            await this.runCommand('npm install');
        }
        
        // 检查关键依赖
        const requiredDependencies = ['express', 'socket.io', 'cors'];
        for (const dep of requiredDependencies) {
            const depPath = path.join(nodeModulesPath, dep);
            if (!fs.existsSync(depPath)) {
                throw new Error(`缺少关键依赖: ${dep}`);
            }
        }
        
        this.addDeploymentStep('依赖检查', true, '所有依赖检查通过');
        console.log('✅ 依赖检查完成\n');
    }

    // 文件完整性检查
    async checkFileIntegrity() {
        console.log('📁 检查文件完整性...');
        
        const requiredFiles = [
            'backend/app.js',
            'frontend/index.html',
            'frontend/css/styles.css',
            'frontend/js/config.js',
            'frontend/js/app.js'
        ];
        
        const missingFiles = [];
        
        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                missingFiles.push(file);
            } else {
                console.log(`✓ ${file}`);
            }
        }
        
        if (missingFiles.length > 0) {
            throw new Error(`缺少关键文件: ${missingFiles.join(', ')}`);
        }
        
        this.addDeploymentStep('文件完整性检查', true, '所有关键文件存在');
        console.log('✅ 文件完整性检查完成\n');
    }

    // 配置验证
    async validateConfiguration() {
        console.log('⚙️ 验证配置...');
        
        // 检查端口是否可用
        const isPortAvailable = await this.checkPortAvailability(this.deploymentConfig.port);
        if (!isPortAvailable) {
            throw new Error(`端口 ${this.deploymentConfig.port} 已被占用`);
        }
        
        console.log(`✓ 端口 ${this.deploymentConfig.port} 可用`);
        console.log(`✓ 主机地址: ${this.deploymentConfig.host}`);
        console.log(`✓ 环境: ${this.deploymentConfig.environment}`);
        
        this.addDeploymentStep('配置验证', true, '配置验证通过');
        console.log('✅ 配置验证完成\n');
    }

    // 启动服务
    async startService() {
        console.log('🚀 启动服务...');
        
        const appPath = path.join(this.projectRoot, 'backend/app.js');
        
        return new Promise((resolve, reject) => {
            const serverProcess = spawn('node', [appPath], {
                cwd: this.projectRoot,
                stdio: 'pipe',
                detached: false
            });
            
            let startupTimeout = setTimeout(() => {
                serverProcess.kill();
                reject(new Error('服务启动超时'));
            }, 10000);
            
            serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log(output.trim());
                
                if (output.includes('服务器运行在') || output.includes('Server running')) {
                    clearTimeout(startupTimeout);
                    this.serverProcess = serverProcess;
                    this.addDeploymentStep('服务启动', true, '服务启动成功');
                    console.log('✅ 服务启动完成\n');
                    resolve();
                }
            });
            
            serverProcess.stderr.on('data', (data) => {
                console.error('服务器错误:', data.toString());
            });
            
            serverProcess.on('error', (error) => {
                clearTimeout(startupTimeout);
                reject(new Error(`服务启动失败: ${error.message}`));
            });
        });
    }

    // 健康检查
    async healthCheck() {
        console.log('🏥 执行健康检查...');
        
        // 等待服务完全启动
        await this.sleep(2000);
        
        const http = require('http');
        
        return new Promise((resolve, reject) => {
            const options = {
                hostname: this.deploymentConfig.host,
                port: this.deploymentConfig.port,
                path: '/',
                method: 'GET',
                timeout: 5000
            };
            
            const req = http.request(options, (res) => {
                if (res.statusCode === 200) {
                    console.log('✓ HTTP服务响应正常');
                    this.addDeploymentStep('健康检查', true, 'HTTP服务健康');
                    console.log('✅ 健康检查完成\n');
                    resolve();
                } else {
                    reject(new Error(`健康检查失败，状态码: ${res.statusCode}`));
                }
            });
            
            req.on('error', (error) => {
                reject(new Error(`健康检查失败: ${error.message}`));
            });
            
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('健康检查超时'));
            });
            
            req.end();
        });
    }

    // 生成部署报告
    generateDeploymentReport() {
        const report = {
            deploymentTime: new Date().toISOString(),
            environment: this.deploymentConfig.environment,
            configuration: this.deploymentConfig,
            steps: this.deploymentSteps,
            status: 'success',
            serviceUrl: `http://${this.deploymentConfig.host}:${this.deploymentConfig.port}`
        };
        
        const reportPath = path.join(this.projectRoot, 'docs/deployment-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('📋 部署报告:');
        console.log(`- 部署时间: ${report.deploymentTime}`);
        console.log(`- 服务地址: ${report.serviceUrl}`);
        console.log(`- 执行步骤: ${this.deploymentSteps.length}`);
        console.log(`- 报告文件: ${reportPath}`);
    }

    // 回滚
    rollback() {
        console.log('🔄 执行回滚...');
        
        if (this.serverProcess) {
            this.serverProcess.kill();
            console.log('✓ 服务已停止');
        }
        
        console.log('✅ 回滚完成');
    }

    // 工具方法
    isNodeVersionSupported(version) {
        const major = parseInt(version.slice(1).split('.')[0]);
        return major >= 14;
    }

    async checkPortAvailability(port) {
        const net = require('net');
        
        return new Promise((resolve) => {
            const server = net.createServer();
            
            server.listen(port, () => {
                server.once('close', () => {
                    resolve(true);
                });
                server.close();
            });
            
            server.on('error', () => {
                resolve(false);
            });
        });
    }

    async runCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, { cwd: this.projectRoot }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    console.log(stdout);
                    resolve(stdout);
                }
            });
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    addDeploymentStep(name, success, message) {
        this.deploymentSteps.push({
            name,
            success,
            message,
            timestamp: new Date().toISOString()
        });
    }

    // 停止服务
    stop() {
        if (this.serverProcess) {
            console.log('🛑 停止服务...');
            this.serverProcess.kill();
            console.log('✅ 服务已停止');
        }
    }
}

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n收到退出信号，正在停止服务...');
    if (global.deploymentManager) {
        global.deploymentManager.stop();
    }
    process.exit(0);
});

// 运行部署
if (require.main === module) {
    const deploymentManager = new DeploymentManager();
    global.deploymentManager = deploymentManager;
    deploymentManager.deploy().catch(console.error);
}

module.exports = DeploymentManager;
