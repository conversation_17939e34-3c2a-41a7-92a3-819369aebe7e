/* 苹果风格现代化设计 - 主样式文件 */

:root {
    /* 苹果设计系统颜色 - 浅色模式 */
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    --info-color: #5AC8FA;

    /* 中性色 - 浅色模式 */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #F2F2F7;
    --background-secondary: #FFFFFF;
    --background-tertiary: rgba(255, 255, 255, 0.8);
    --background-quaternary: rgba(255, 255, 255, 0.6);

    /* 毛玻璃效果 */
    --glass-background: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-blur: blur(20px);

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    --spacing-xxxl: 64px;

    /* 圆角系统 */
    --radius-xs: 4px;
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-xxl: 24px;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.15);

    /* 字体系统 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
    --font-size-xxxl: 40px;

    /* 动画系统 */
    --transition-fast: 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-normal: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-slow: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* Z-index系统 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
    :root {
        /* 苹果设计系统颜色 - 暗色模式 */
        --primary-color: #0A84FF;
        --secondary-color: #5E5CE6;
        --success-color: #30D158;
        --warning-color: #FF9F0A;
        --error-color: #FF453A;
        --info-color: #64D2FF;

        /* 中性色 - 暗色模式 */
        --text-primary: #FFFFFF;
        --text-secondary: #98989D;
        --text-tertiary: #48484A;
        --background-primary: #000000;
        --background-secondary: #1C1C1E;
        --background-tertiary: rgba(28, 28, 30, 0.8);
        --background-quaternary: rgba(28, 28, 30, 0.6);

        /* 毛玻璃效果 - 暗色模式 */
        --glass-background: rgba(28, 28, 30, 0.8);
        --glass-border: rgba(255, 255, 255, 0.1);
        --glass-blur: blur(20px);

        /* 阴影系统 - 暗色模式 */
        --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.3);
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4);
        --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.4);
        --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.5);
    }
}

/* 手动暗色模式切换 */
[data-theme="dark"] {
    /* 苹果设计系统颜色 - 暗色模式 */
    --primary-color: #0A84FF;
    --secondary-color: #5E5CE6;
    --success-color: #30D158;
    --warning-color: #FF9F0A;
    --error-color: #FF453A;
    --info-color: #64D2FF;

    /* 中性色 - 暗色模式 */
    --text-primary: #FFFFFF;
    --text-secondary: #98989D;
    --text-tertiary: #48484A;
    --background-primary: #000000;
    --background-secondary: #1C1C1E;
    --background-tertiary: rgba(28, 28, 30, 0.8);
    --background-quaternary: rgba(28, 28, 30, 0.6);

    /* 毛玻璃效果 - 暗色模式 */
    --glass-background: rgba(28, 28, 30, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-blur: blur(20px);

    /* 阴影系统 - 暗色模式 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.5);
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-normal);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        var(--primary-color) 0%,
        var(--secondary-color) 100%);
    opacity: 0.03;
    pointer-events: none;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.logo .version {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

.header-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.time-display {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--glass-background);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    backdrop-filter: var(--glass-blur);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.connection-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left var(--transition-slow);
}

.connection-status:hover::before {
    left: 100%;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--warning-color);
    animation: pulse 2s infinite;
    position: relative;
    box-shadow: 0 0 8px currentColor;
}

.status-dot.connected {
    background: var(--success-color);
    animation: none;
}

.status-dot.disconnected {
    background: var(--error-color);
    animation: pulse 1s infinite;
}

.status-dot.connecting {
    background: var(--info-color);
    animation: pulse 1.5s infinite;
}

.status-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    width: 100%;
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: 0 var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .header-info {
        gap: var(--spacing-md);
    }
    
    .main-content {
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .logo h1 {
        font-size: var(--font-size-lg);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}
