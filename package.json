{"name": "loong-terminal-web", "version": "1.0.0", "description": "智能终端管理系统Web版本 - 基于Qt项目的前后端分离架构", "main": "backend/app.js", "scripts": {"start": "node backend/app.js", "dev": "nodemon backend/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["iot", "sensor", "mqtt", "websocket", "terminal-management"], "author": "Loong Terminal System", "license": "MIT", "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "helmet": "^7.1.0", "mqtt": "^5.3.4", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}