// 图表工具类 - 提供图表相关的工具函数
class ChartUtils {
    // 生成渐变色
    static createGradient(ctx, color1, color2, direction = 'vertical') {
        const gradient = direction === 'vertical' 
            ? ctx.createLinearGradient(0, 0, 0, 400)
            : ctx.createLinearGradient(0, 0, 400, 0);
        
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        return gradient;
    }

    // 获取传感器颜色主题
    static getSensorColorTheme(sensorType) {
        const themes = {
            temperature: {
                primary: '#FF6B6B',
                secondary: '#FF8E8E',
                gradient: ['#FF6B6B', '#FFE66D']
            },
            humidity: {
                primary: '#4ECDC4',
                secondary: '#6EDDD6',
                gradient: ['#4ECDC4', '#44A08D']
            },
            light: {
                primary: '#FFE66D',
                secondary: '#FFED8A',
                gradient: ['#FFE66D', '#FF6B6B']
            },
            smoke: {
                primary: '#A8E6CF',
                secondary: '#B8F0D6',
                gradient: ['#A8E6CF', '#7FCDCD']
            }
        };
        
        return themes[sensorType] || themes.temperature;
    }

    // 创建动画配置
    static createAnimationConfig(type = 'default') {
        const configs = {
            default: {
                duration: 750,
                easing: 'easeInOutQuart'
            },
            fast: {
                duration: 300,
                easing: 'easeOutQuart'
            },
            slow: {
                duration: 1500,
                easing: 'easeInOutCubic'
            },
            bounce: {
                duration: 1000,
                easing: 'easeOutBounce'
            }
        };
        
        return configs[type] || configs.default;
    }

    // 创建响应式配置
    static createResponsiveConfig() {
        return {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: window.devicePixelRatio || 1,
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        };
    }

    // 创建工具提示配置
    static createTooltipConfig(sensorType) {
        const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
        
        return {
            enabled: true,
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: ChartUtils.getSensorColorTheme(sensorType).primary,
            borderWidth: 2,
            cornerRadius: 8,
            displayColors: true,
            callbacks: {
                title: (context) => {
                    return `时间: ${context[0].label}`;
                },
                label: (context) => {
                    return `${context.dataset.label}: ${context.parsed.y}${sensorConfig.unit}`;
                },
                afterLabel: (context) => {
                    // 添加额外信息，如警告状态
                    const value = context.parsed.y;
                    const limits = CONFIG.sensorLimits[sensorType];
                    
                    if (limits) {
                        if (value > limits.max || value < limits.min) {
                            return '⚠️ 超出正常范围';
                        } else {
                            return '✅ 正常范围';
                        }
                    }
                    return '';
                }
            }
        };
    }

    // 创建图例配置
    static createLegendConfig() {
        return {
            display: true,
            position: 'top',
            align: 'center',
            labels: {
                usePointStyle: true,
                pointStyle: 'circle',
                padding: 20,
                font: {
                    size: 12,
                    weight: '500'
                },
                color: getComputedStyle(document.documentElement)
                    .getPropertyValue('--text-secondary').trim(),
                generateLabels: (chart) => {
                    const original = Chart.defaults.plugins.legend.labels.generateLabels;
                    const labels = original.call(this, chart);
                    
                    // 为每个标签添加在线状态指示
                    labels.forEach((label, index) => {
                        const nodeId = index + 1;
                        const nodeManager = window.app?.getManager('nodes');
                        const node = nodeManager?.getNode(nodeId);
                        
                        if (node && !node.status) {
                            label.text += ' (离线)';
                            label.fontColor = '#999';
                        }
                    });
                    
                    return labels;
                }
            }
        };
    }

    // 创建坐标轴配置
    static createScalesConfig(sensorType) {
        const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
        const yRange = CONFIG.charts.yAxisRanges[sensorType];
        
        return {
            x: {
                type: 'category',
                display: true,
                title: {
                    display: true,
                    text: '时间',
                    font: {
                        size: 14,
                        weight: '600'
                    },
                    color: getComputedStyle(document.documentElement)
                        .getPropertyValue('--text-secondary').trim()
                },
                grid: {
                    display: true,
                    color: getComputedStyle(document.documentElement)
                        .getPropertyValue('--text-tertiary').trim() + '40',
                    lineWidth: 1
                },
                ticks: {
                    maxTicksLimit: 10,
                    color: getComputedStyle(document.documentElement)
                        .getPropertyValue('--text-secondary').trim(),
                    font: {
                        size: 11
                    }
                }
            },
            y: {
                type: 'linear',
                display: true,
                title: {
                    display: true,
                    text: `${sensorConfig.name} (${sensorConfig.unit})`,
                    font: {
                        size: 14,
                        weight: '600'
                    },
                    color: getComputedStyle(document.documentElement)
                        .getPropertyValue('--text-secondary').trim()
                },
                min: yRange.min,
                max: yRange.max,
                grid: {
                    display: true,
                    color: getComputedStyle(document.documentElement)
                        .getPropertyValue('--text-tertiary').trim() + '40',
                    lineWidth: 1
                },
                ticks: {
                    color: getComputedStyle(document.documentElement)
                        .getPropertyValue('--text-secondary').trim(),
                    font: {
                        size: 11
                    },
                    callback: function(value) {
                        return value + sensorConfig.unit;
                    }
                }
            }
        };
    }

    // 创建数据集样式
    static createDatasetStyle(nodeId, sensorType, data) {
        const theme = ChartUtils.getSensorColorTheme(sensorType);
        const baseColor = theme.primary;
        
        // 根据节点ID调整颜色
        const colorVariations = [
            baseColor,
            ChartUtils.adjustColorBrightness(baseColor, 0.3),
            ChartUtils.adjustColorBrightness(baseColor, 0.6)
        ];
        
        const color = colorVariations[nodeId - 1] || baseColor;
        
        return {
            label: `节点${nodeId}`,
            data: data,
            borderColor: color,
            backgroundColor: color + '20',
            borderWidth: 2,
            tension: 0.4,
            pointRadius: 3,
            pointHoverRadius: 6,
            pointBackgroundColor: color,
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointHoverBackgroundColor: color,
            pointHoverBorderColor: '#fff',
            pointHoverBorderWidth: 3,
            fill: true
        };
    }

    // 调整颜色亮度
    static adjustColorBrightness(color, factor) {
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        
        const newR = Math.round(r + (255 - r) * factor);
        const newG = Math.round(g + (255 - g) * factor);
        const newB = Math.round(b + (255 - b) * factor);
        
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }

    // 创建警告区域插件
    static createAlertZonePlugin(sensorType) {
        const limits = CONFIG.sensorLimits[sensorType];
        if (!limits) return null;
        
        return {
            id: 'alertZones',
            beforeDraw: (chart) => {
                const ctx = chart.ctx;
                const chartArea = chart.chartArea;
                const yScale = chart.scales.y;
                
                // 绘制警告区域
                ctx.save();
                ctx.globalAlpha = 0.1;
                
                // 上限警告区域
                if (limits.max !== undefined) {
                    const yMax = yScale.getPixelForValue(limits.max);
                    ctx.fillStyle = '#FF3B30';
                    ctx.fillRect(chartArea.left, chartArea.top, chartArea.width, yMax - chartArea.top);
                }
                
                // 下限警告区域
                if (limits.min !== undefined) {
                    const yMin = yScale.getPixelForValue(limits.min);
                    ctx.fillStyle = '#FF3B30';
                    ctx.fillRect(chartArea.left, yMin, chartArea.width, chartArea.bottom - yMin);
                }
                
                ctx.restore();
            }
        };
    }

    // 格式化数据点
    static formatDataPoint(value, sensorType) {
        const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
        const precision = sensorType === 'light' ? 0 : 1;
        
        return {
            value: Number(value.toFixed(precision)),
            formatted: `${Number(value.toFixed(precision))}${sensorConfig.unit}`,
            timestamp: new Date().toISOString()
        };
    }

    // 计算数据统计
    static calculateDataStatistics(data) {
        if (!data || data.length === 0) {
            return {
                min: 0,
                max: 0,
                avg: 0,
                count: 0
            };
        }
        
        const values = data.filter(v => typeof v === 'number' && !isNaN(v));
        
        return {
            min: Math.min(...values),
            max: Math.max(...values),
            avg: values.reduce((sum, val) => sum + val, 0) / values.length,
            count: values.length
        };
    }
}

// 导出图表工具类
window.ChartUtils = ChartUtils;
