# 华为云IoT平台数据上报格式说明

## 修改内容

已成功修改`TaskSendToCloud()`函数，将数据上报格式调整为华为云IoT平台标准格式。

## 华为云IoT平台消息格式

### JSON消息结构
```json
{
  "services": [
    {
      "service_id": "NodeSensor",
      "properties": {
        "temperature": 25.6,  // 节点一温度
        "humidity": 60.2,     // 节点一湿度
        "light": 800,         // 节点一光照
        "smoke": 15.3         // 节点一烟雾浓度
      }
    }
  ]
}
```

### 字段说明

- **services**: 服务数组，包含设备的各种服务
- **service_id**: "NodeSensor" - 传感器服务标识符
- **properties**: 属性对象，包含具体的传感器数据

### 上报数据映射

| 华为云属性名 | 对应变量 | 说明 |
|-------------|----------|------|
| temperature | stringTemp1 | 节点一温度值 |
| humidity | stringHumi1 | 节点一湿度值 |
| light | stringLight1 | 节点一光照强度值 |
| smoke | stringSmog1 | 节点一烟雾浓度值 |

## 主要改进

### 1. 格式标准化
- 使用华为云IoT平台标准的属性上报格式
- JSON格式规范，便于云平台解析

### 2. 数据精简
- 只上报节点一的四个关键传感器数据
- 移除了节点二、节点三和错误信息
- 减少数据传输量，提高效率

### 3. 属性命名优化
- 使用英文属性名，符合国际化标准
- 属性名简洁明了，便于理解和使用

## 上报主题

**主题**: `$oc/devices/{设备ID}/sys/properties/report`

您的设备上报主题为：
`$oc/devices/685a734ad582f2001834985f_loong_1/sys/properties/report`

## 使用说明

1. 确保华为云IoT平台设备已正确配置
2. 在设备模型中定义对应的属性：
   - temperature (数值型)
   - humidity (数值型)  
   - light (数值型)
   - smoke (数值型)
3. 调用`TaskSendToCloud()`函数即可上报数据

数据将自动按照华为云IoT平台格式发送到云端。
