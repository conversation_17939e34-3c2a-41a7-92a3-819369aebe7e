// MQTT服务管理器 - 集成MQTT客户端和华为云IoT适配器
const MQTTClient = require('./mqttClient');
const HuaweiIoTAdapter = require('../adapters/huaweiIoT');
const dataManager = require('./dataManager');
const EventEmitter = require('events');

class MQTTService extends EventEmitter {
    constructor() {
        super();
        this.mqttClient = null;
        this.adapter = new HuaweiIoTAdapter();
        this.isRunning = false;
        this.config = null;
        this.healthCheckInterval = null;
        this.messageQueue = [];
        this.maxQueueSize = 1000;
        this.statistics = {
            startTime: null,
            messagesReceived: 0,
            messagesSent: 0,
            errors: 0,
            reconnections: 0
        };
    }

    // 初始化MQTT服务
    async initialize() {
        try {
            console.log('🔧 初始化MQTT服务...');
            
            // 创建MQTT客户端
            this.mqttClient = new MQTTClient();
            
            // 设置事件监听器
            this.setupEventHandlers();
            
            console.log('✅ MQTT服务初始化完成');
            return true;
        } catch (error) {
            console.error('❌ MQTT服务初始化失败:', error.message);
            this.emit('error', error);
            return false;
        }
    }

    // 设置事件处理器
    setupEventHandlers() {
        // MQTT连接事件
        this.mqttClient.on('connected', () => {
            this.isRunning = true;
            this.statistics.startTime = new Date();
            console.log('✅ MQTT服务已连接');
            this.emit('connected');
            this.startHealthCheck();
        });

        this.mqttClient.on('disconnected', () => {
            this.isRunning = false;
            console.log('📱 MQTT服务已断开');
            this.emit('disconnected');
            this.stopHealthCheck();
        });

        this.mqttClient.on('error', (error) => {
            this.statistics.errors++;
            console.error('❌ MQTT服务错误:', error.message);
            this.emit('error', error);
        });

        this.mqttClient.on('reconnect', () => {
            this.statistics.reconnections++;
            console.log('🔄 MQTT服务重连中...');
            this.emit('reconnecting');
        });

        // 消息处理事件
        this.mqttClient.on('message', (messageData) => {
            this.handleIncomingMessage(messageData);
        });

        this.mqttClient.on('sensorData', (sensorData) => {
            this.handleSensorData(sensorData);
        });

        this.mqttClient.on('published', (publishData) => {
            this.statistics.messagesSent++;
            this.emit('messageSent', publishData);
        });
    }

    // 处理接收到的消息
    handleIncomingMessage(messageData) {
        try {
            this.statistics.messagesReceived++;
            
            // 使用适配器解析华为云IoT消息
            const parseResult = this.adapter.fromHuaweiIoTFormat(messageData.message);
            
            if (parseResult.success) {
                parseResult.data.forEach(item => {
                    // 更新数据管理器
                    dataManager.updateNodeData(item.nodeId, item.sensorData);
                    console.log(`📊 通过MQTT更新节点${item.nodeId}数据`);
                });
                
                this.emit('dataReceived', parseResult.data);
            } else {
                console.error('❌ MQTT消息解析失败:', parseResult.error);
            }
        } catch (error) {
            console.error('❌ MQTT消息处理失败:', error.message);
            this.statistics.errors++;
        }
    }

    // 处理传感器数据
    handleSensorData(sensorData) {
        try {
            console.log(`📊 收到传感器数据 - 节点${sensorData.nodeId}:`, sensorData.data);
            this.emit('sensorDataReceived', sensorData);
        } catch (error) {
            console.error('❌ 传感器数据处理失败:', error.message);
        }
    }

    // 启动MQTT服务
    async start() {
        if (this.isRunning) {
            console.log('⚠️ MQTT服务已在运行中');
            return true;
        }

        try {
            if (!this.mqttClient) {
                await this.initialize();
            }

            console.log('🚀 启动MQTT服务...');
            this.mqttClient.connect();
            
            return true;
        } catch (error) {
            console.error('❌ MQTT服务启动失败:', error.message);
            this.emit('error', error);
            return false;
        }
    }

    // 停止MQTT服务
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ MQTT服务未运行');
            return;
        }

        console.log('🛑 停止MQTT服务...');
        
        this.stopHealthCheck();
        
        if (this.mqttClient) {
            this.mqttClient.disconnect();
        }
        
        this.isRunning = false;
        this.emit('stopped');
    }

    // 发送传感器数据到华为云
    async sendSensorData(nodeId, sensorData) {
        if (!this.isRunning) {
            throw new Error('MQTT服务未运行');
        }

        try {
            // 使用适配器转换数据格式
            const convertResult = this.adapter.toHuaweiIoTFormat(nodeId, sensorData);
            
            if (!convertResult.success) {
                throw new Error(convertResult.error);
            }

            // 发布到华为云IoT
            const success = this.mqttClient.publishMessage(convertResult.data.services[0].properties);
            
            if (success) {
                console.log(`📤 成功发送节点${nodeId}数据到华为云`);
                return true;
            } else {
                throw new Error('消息发布失败');
            }
        } catch (error) {
            console.error(`❌ 发送节点${nodeId}数据失败:`, error.message);
            this.statistics.errors++;
            throw error;
        }
    }

    // 批量发送数据
    async sendBatchData(dataArray) {
        const results = [];
        const errors = [];

        for (const item of dataArray) {
            try {
                await this.sendSensorData(item.nodeId, item.sensorData);
                results.push({ nodeId: item.nodeId, success: true });
            } catch (error) {
                errors.push({ nodeId: item.nodeId, error: error.message });
            }
        }

        return {
            success: errors.length === 0,
            results,
            errors,
            totalSent: results.length,
            totalErrors: errors.length
        };
    }

    // 开始健康检查
    startHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }

        this.healthCheckInterval = setInterval(() => {
            this.performHealthCheck();
        }, 60000); // 每分钟检查一次

        console.log('💓 MQTT健康检查已启动');
    }

    // 停止健康检查
    stopHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            console.log('💓 MQTT健康检查已停止');
        }
    }

    // 执行健康检查
    performHealthCheck() {
        const status = this.getStatus();
        
        if (!status.isConnected) {
            console.log('⚠️ MQTT连接异常，尝试重连...');
            this.mqttClient.reconnect();
        }

        this.emit('healthCheck', status);
    }

    // 获取服务状态
    getStatus() {
        const mqttStatus = this.mqttClient ? this.mqttClient.getConnectionStatus() : null;
        const adapterStats = this.adapter.getStatistics();

        return {
            isRunning: this.isRunning,
            isConnected: mqttStatus ? mqttStatus.isConnected : false,
            statistics: this.statistics,
            mqttClient: mqttStatus,
            adapter: adapterStats,
            messageQueue: {
                size: this.messageQueue.length,
                maxSize: this.maxQueueSize
            },
            uptime: this.statistics.startTime ? 
                Date.now() - this.statistics.startTime.getTime() : 0
        };
    }

    // 获取详细统计信息
    getDetailedStatistics() {
        const status = this.getStatus();
        
        return {
            ...status,
            performance: {
                messagesPerMinute: this.calculateMessagesPerMinute(),
                errorRate: this.calculateErrorRate(),
                averageResponseTime: this.calculateAverageResponseTime()
            },
            health: {
                connectionStable: status.isConnected && status.mqttClient.reconnectAttempts === 0,
                messageFlowNormal: this.statistics.messagesReceived > 0,
                errorRateAcceptable: this.calculateErrorRate() < 5 // 5%以下
            }
        };
    }

    // 计算每分钟消息数
    calculateMessagesPerMinute() {
        if (!this.statistics.startTime) return 0;
        
        const uptimeMinutes = (Date.now() - this.statistics.startTime.getTime()) / 60000;
        return uptimeMinutes > 0 ? Math.round(this.statistics.messagesReceived / uptimeMinutes) : 0;
    }

    // 计算错误率
    calculateErrorRate() {
        const totalOperations = this.statistics.messagesReceived + this.statistics.messagesSent;
        return totalOperations > 0 ? (this.statistics.errors / totalOperations * 100) : 0;
    }

    // 计算平均响应时间（简化版）
    calculateAverageResponseTime() {
        // 这里返回模拟值，实际应用中需要记录真实的响应时间
        return Math.random() * 100 + 50; // 50-150ms
    }

    // 重置统计信息
    resetStatistics() {
        this.statistics = {
            startTime: new Date(),
            messagesReceived: 0,
            messagesSent: 0,
            errors: 0,
            reconnections: 0
        };
        
        this.adapter.resetStatistics();
        console.log('📊 MQTT服务统计信息已重置');
    }
}

module.exports = MQTTService;
