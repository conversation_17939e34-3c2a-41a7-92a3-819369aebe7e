// WebSocket服务实现 - 复用Qt项目的50ms实时更新模式
const { Server } = require('socket.io');
const dataManager = require('./dataManager');

class WebSocketService {
    constructor(server) {
        this.io = new Server(server, {
            cors: {
                origin: ['http://127.0.0.1:3000', 'http://localhost:3000'],
                methods: ['GET', 'POST'],
                credentials: true
            },
            transports: ['websocket', 'polling'],
            pingTimeout: 60000,
            pingInterval: 25000
        });

        this.clients = new Map(); // 存储客户端连接信息
        this.rooms = new Map(); // 房间管理
        this.updateInterval = null; // 定时器
        this.lastDataSnapshot = null; // 上次数据快照
        
        this.setupEventHandlers();
        this.startDataStreaming();
        
        console.log('✅ WebSocket服务初始化完成');
    }

    // 设置事件处理器
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            this.handleClientConnection(socket);
        });
    }

    // 处理客户端连接
    handleClientConnection(socket) {
        const clientInfo = {
            id: socket.id,
            connectedAt: new Date(),
            lastActivity: new Date(),
            subscribedNodes: new Set(),
            userAgent: socket.handshake.headers['user-agent'] || 'Unknown'
        };

        this.clients.set(socket.id, clientInfo);
        
        console.log(`📱 客户端连接: ${socket.id} (总连接数: ${this.clients.size})`);

        // 发送初始数据
        this.sendInitialData(socket);

        // 处理客户端事件
        this.setupClientEventHandlers(socket);

        // 处理断开连接
        socket.on('disconnect', (reason) => {
            this.handleClientDisconnection(socket, reason);
        });
    }

    // 设置客户端事件处理器
    setupClientEventHandlers(socket) {
        // 订阅节点数据
        socket.on('subscribe-node', (nodeId) => {
            const client = this.clients.get(socket.id);
            if (client && [1, 2, 3].includes(Number(nodeId))) {
                client.subscribedNodes.add(Number(nodeId));
                socket.join(`node-${nodeId}`);
                console.log(`📡 客户端 ${socket.id} 订阅节点 ${nodeId}`);
                
                // 发送该节点的当前数据
                const nodeData = dataManager.getNode(nodeId);
                if (nodeData) {
                    socket.emit('node-data', { nodeId: Number(nodeId), data: nodeData });
                }
            }
        });

        // 取消订阅节点
        socket.on('unsubscribe-node', (nodeId) => {
            const client = this.clients.get(socket.id);
            if (client) {
                client.subscribedNodes.delete(Number(nodeId));
                socket.leave(`node-${nodeId}`);
                console.log(`📡 客户端 ${socket.id} 取消订阅节点 ${nodeId}`);
            }
        });

        // 订阅所有节点
        socket.on('subscribe-all', () => {
            const client = this.clients.get(socket.id);
            if (client) {
                for (let i = 1; i <= 3; i++) {
                    client.subscribedNodes.add(i);
                    socket.join(`node-${i}`);
                }
                socket.join('all-nodes');
                console.log(`📡 客户端 ${socket.id} 订阅所有节点`);
                
                // 发送所有节点数据
                const allNodes = dataManager.getAllNodes();
                socket.emit('all-nodes-data', allNodes);
            }
        });

        // 心跳检测
        socket.on('ping', () => {
            const client = this.clients.get(socket.id);
            if (client) {
                client.lastActivity = new Date();
            }
            socket.emit('pong', { timestamp: new Date().toISOString() });
        });

        // 请求系统状态
        socket.on('get-system-status', () => {
            const systemStatus = dataManager.getSystemStatus();
            socket.emit('system-status', systemStatus);
        });

        // 请求警告摘要
        socket.on('get-alerts-summary', () => {
            const alerts = dataManager.getAllActiveAlerts();
            socket.emit('alerts-summary', alerts);
        });
    }

    // 发送初始数据
    sendInitialData(socket) {
        try {
            // 发送所有节点状态
            const allNodes = dataManager.getAllNodes();
            socket.emit('initial-data', {
                nodes: allNodes,
                systemStatus: dataManager.getSystemStatus(),
                alerts: dataManager.getAllActiveAlerts(),
                timestamp: new Date().toISOString()
            });

            // 发送连接确认
            socket.emit('connection-confirmed', {
                clientId: socket.id,
                serverTime: new Date().toISOString(),
                updateInterval: 50 // 50ms更新间隔
            });
        } catch (error) {
            console.error('❌ 发送初始数据失败:', error);
            socket.emit('error', { message: '初始数据加载失败' });
        }
    }

    // 处理客户端断开连接
    handleClientDisconnection(socket, reason) {
        const client = this.clients.get(socket.id);
        if (client) {
            const connectionDuration = Date.now() - client.connectedAt.getTime();
            console.log(`📱 客户端断开: ${socket.id} (原因: ${reason}, 连接时长: ${Math.round(connectionDuration/1000)}s)`);
            
            // 清理客户端信息
            this.clients.delete(socket.id);
        }
    }

    // 开始数据流推送 - 复用Qt项目的50ms更新频率
    startDataStreaming() {
        console.log('🔄 启动数据流推送服务 (50ms间隔)');
        
        this.updateInterval = setInterval(() => {
            this.broadcastDataUpdates();
        }, 50); // 50ms间隔，复用Qt项目设置
    }

    // 广播数据更新
    broadcastDataUpdates() {
        try {
            // 获取当前数据
            const currentData = {
                nodes: dataManager.getAllNodes(),
                systemStatus: dataManager.getSystemStatus(),
                timestamp: new Date().toISOString()
            };

            // 检查数据是否有变化
            if (this.hasDataChanged(currentData)) {
                // 广播到所有连接的客户端
                this.io.emit('data-update', {
                    nodes: currentData.nodes,
                    timestamp: currentData.timestamp
                });

                // 广播系统状态更新
                this.io.emit('system-status-update', currentData.systemStatus);

                // 检查并广播警告
                this.checkAndBroadcastAlerts();

                // 更新数据快照
                this.lastDataSnapshot = JSON.stringify(currentData);
            }
        } catch (error) {
            console.error('❌ 数据广播失败:', error);
        }
    }

    // 检查数据是否有变化
    hasDataChanged(currentData) {
        const currentSnapshot = JSON.stringify(currentData);
        if (this.lastDataSnapshot !== currentSnapshot) {
            return true;
        }
        return false;
    }

    // 检查并广播警告
    checkAndBroadcastAlerts() {
        const alerts = dataManager.getAllActiveAlerts();
        const recentAlerts = alerts.filter(alert => {
            const alertTime = new Date(alert.timestamp);
            const now = new Date();
            return (now - alertTime) < 1000; // 1秒内的新警告
        });

        if (recentAlerts.length > 0) {
            this.io.emit('alert-trigger', {
                alerts: recentAlerts,
                timestamp: new Date().toISOString()
            });
        }
    }

    // 向特定节点房间广播
    broadcastToNodeRoom(nodeId, event, data) {
        this.io.to(`node-${nodeId}`).emit(event, {
            nodeId,
            data,
            timestamp: new Date().toISOString()
        });
    }

    // 获取连接统计
    getConnectionStats() {
        const stats = {
            totalConnections: this.clients.size,
            activeConnections: 0,
            roomStats: {},
            clientDetails: []
        };

        // 统计活跃连接
        const now = new Date();
        for (const [clientId, client] of this.clients) {
            const inactiveTime = now - client.lastActivity;
            if (inactiveTime < 60000) { // 1分钟内活跃
                stats.activeConnections++;
            }

            stats.clientDetails.push({
                id: clientId,
                connectedAt: client.connectedAt,
                lastActivity: client.lastActivity,
                subscribedNodes: Array.from(client.subscribedNodes),
                inactiveTime: Math.round(inactiveTime / 1000)
            });
        }

        // 统计房间信息
        for (let i = 1; i <= 3; i++) {
            const room = this.io.sockets.adapter.rooms.get(`node-${i}`);
            stats.roomStats[`node-${i}`] = room ? room.size : 0;
        }

        return stats;
    }

    // 停止WebSocket服务
    stop() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        this.io.close();
        console.log('🛑 WebSocket服务已停止');
    }
}

module.exports = WebSocketService;
