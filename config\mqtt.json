{"huaweiCloud": {"deviceId": "685a734ad582f2001834985f_loong_1", "deviceSecret": "3246a4eab894b351f7d199a8d2f9aa302fece2d1204e126c429e044f1a1a082a", "hostname": "5930c00e73.st1.iotda-device.cn-north-4.myhuaweicloud.com", "port": 8883, "clientId": "685a734ad582f2001834985f_loong_1_0_0_2025062508", "keepAlive": 120, "topics": {"publish": "$oc/devices/685a734ad582f2001834985f_loong_1/sys/properties/report", "subscribe": "$oc/devices/685a734ad582f2001834985f_loong_1/sys/properties/set", "command": "$oc/devices/685a734ad582f2001834985f_loong_1/sys/commands/#", "response": "$oc/devices/685a734ad582f2001834985f_loong_1/sys/commands/response"}, "ssl": true, "autoReconnect": true, "reconnectPeriod": 5000, "connectTimeout": 30000}, "dataFormat": {"serviceId": "SensorData", "properties": {"nodeId": "number", "temperature": "number", "humidity": "number", "light": "number", "smoke": "number", "timestamp": "string"}, "eventTimeFormat": "YYYYMMDDTHHMMSSZ"}, "sensorLimits": {"temperature": {"min": 0, "max": 50}, "humidity": {"min": 10, "max": 500}, "light": {"min": 10, "max": 800}, "smoke": {"min": 0, "max": 600}}, "connection": {"enabled": true, "autoStart": false, "maxReconnectAttempts": 10, "healthCheckInterval": 60000, "messageQueueSize": 1000}, "logging": {"enabled": true, "level": "info", "logMessages": true, "logErrors": true}}