// MQTT测试 - 验证华为云IoT集成功能
const MQTTService = require('../services/mqttService');
const HuaweiIoTAdapter = require('../adapters/huaweiIoT');
const dataManager = require('../services/dataManager');

class MQTTTester {
    constructor() {
        this.mqttService = null;
        this.adapter = new HuaweiIoTAdapter();
        this.testResults = [];
        this.isRunning = false;
    }

    // 运行MQTT测试
    async runTests() {
        console.log('🧪 开始MQTT华为云IoT集成测试...\n');
        this.isRunning = true;

        try {
            // 测试1: 华为云IoT适配器测试
            await this.testHuaweiIoTAdapter();

            // 测试2: MQTT配置加载测试
            await this.testMQTTConfiguration();

            // 测试3: MQTT服务初始化测试
            await this.testMQTTServiceInitialization();

            // 测试4: 数据格式转换测试
            await this.testDataFormatConversion();

            // 测试5: 消息验证测试
            await this.testMessageValidation();

            // 测试6: 批量数据处理测试
            await this.testBatchDataProcessing();

            // 测试7: 错误处理测试
            await this.testErrorHandling();

            // 测试8: 统计信息测试
            await this.testStatistics();

            console.log('\n🎉 MQTT测试完成！');
            this.printTestSummary();

        } catch (error) {
            console.error('❌ MQTT测试失败:', error);
        } finally {
            this.cleanup();
            this.isRunning = false;
        }
    }

    // 测试华为云IoT适配器
    async testHuaweiIoTAdapter() {
        console.log('🔄 测试华为云IoT适配器...');
        
        try {
            // 测试数据转换
            const testData = {
                temperature: 25.5,
                humidity: 60.2,
                light: 500,
                smoke: 20.0
            };

            const convertResult = this.adapter.toHuaweiIoTFormat(1, testData);
            
            if (convertResult.success) {
                console.log('✅ 数据转换为华为云IoT格式成功');
                
                // 验证转换后的数据结构
                const huaweiData = convertResult.data;
                if (huaweiData.services && huaweiData.services.length > 0) {
                    const service = huaweiData.services[0];
                    if (service.service_id === 'SensorData' && service.properties) {
                        console.log('✅ 华为云IoT数据结构验证通过');
                        this.testResults.push({
                            test: 'huawei-iot-adapter',
                            status: 'passed',
                            message: '华为云IoT适配器功能正常'
                        });
                    } else {
                        throw new Error('华为云IoT数据结构不正确');
                    }
                } else {
                    throw new Error('华为云IoT数据格式错误');
                }
            } else {
                throw new Error(convertResult.error);
            }
        } catch (error) {
            console.log('❌ 华为云IoT适配器测试失败:', error.message);
            this.testResults.push({
                test: 'huawei-iot-adapter',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试MQTT配置加载
    async testMQTTConfiguration() {
        console.log('⚙️ 测试MQTT配置加载...');
        
        try {
            const mqttService = new MQTTService();
            await mqttService.initialize();
            
            const status = mqttService.getStatus();
            
            if (status && status.mqttClient !== null) {
                console.log('✅ MQTT配置加载成功');
                this.testResults.push({
                    test: 'mqtt-configuration',
                    status: 'passed',
                    message: 'MQTT配置加载正常'
                });
            } else {
                throw new Error('MQTT配置加载失败');
            }
        } catch (error) {
            console.log('❌ MQTT配置测试失败:', error.message);
            this.testResults.push({
                test: 'mqtt-configuration',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试MQTT服务初始化
    async testMQTTServiceInitialization() {
        console.log('🔧 测试MQTT服务初始化...');
        
        try {
            this.mqttService = new MQTTService();
            const initResult = await this.mqttService.initialize();
            
            if (initResult) {
                console.log('✅ MQTT服务初始化成功');
                this.testResults.push({
                    test: 'mqtt-service-init',
                    status: 'passed',
                    message: 'MQTT服务初始化正常'
                });
            } else {
                throw new Error('MQTT服务初始化失败');
            }
        } catch (error) {
            console.log('❌ MQTT服务初始化测试失败:', error.message);
            this.testResults.push({
                test: 'mqtt-service-init',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试数据格式转换
    async testDataFormatConversion() {
        console.log('🔄 测试数据格式转换...');
        
        try {
            // 测试Web API到华为云IoT格式转换
            const webData = {
                temperature: 28.5,
                humidity: 65.0,
                light: 600,
                smoke: 25.0
            };

            const toHuaweiResult = this.adapter.toHuaweiIoTFormat(2, webData);
            
            if (!toHuaweiResult.success) {
                throw new Error('Web API到华为云IoT格式转换失败');
            }

            // 测试华为云IoT到Web API格式转换
            const huaweiMessage = toHuaweiResult.data;
            const fromHuaweiResult = this.adapter.fromHuaweiIoTFormat(huaweiMessage);
            
            if (!fromHuaweiResult.success) {
                throw new Error('华为云IoT到Web API格式转换失败');
            }

            console.log('✅ 数据格式双向转换成功');
            this.testResults.push({
                test: 'data-format-conversion',
                status: 'passed',
                message: '数据格式转换功能正常'
            });
        } catch (error) {
            console.log('❌ 数据格式转换测试失败:', error.message);
            this.testResults.push({
                test: 'data-format-conversion',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试消息验证
    async testMessageValidation() {
        console.log('✅ 测试消息验证...');
        
        try {
            // 测试有效消息
            const validMessage = {
                services: [{
                    service_id: 'SensorData',
                    properties: {
                        nodeId: 1,
                        temperature: 25.0,
                        humidity: 60.0,
                        light: 500,
                        smoke: 20.0
                    }
                }]
            };

            const validResult = this.adapter.validateHuaweiIoTMessage(validMessage);
            if (!validResult.isValid) {
                throw new Error('有效消息验证失败');
            }

            // 测试无效消息
            const invalidMessage = { invalid: 'data' };
            const invalidResult = this.adapter.validateHuaweiIoTMessage(invalidMessage);
            if (invalidResult.isValid) {
                throw new Error('无效消息验证应该失败');
            }

            console.log('✅ 消息验证功能正常');
            this.testResults.push({
                test: 'message-validation',
                status: 'passed',
                message: '消息验证功能正常'
            });
        } catch (error) {
            console.log('❌ 消息验证测试失败:', error.message);
            this.testResults.push({
                test: 'message-validation',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试批量数据处理
    async testBatchDataProcessing() {
        console.log('📦 测试批量数据处理...');
        
        try {
            const batchData = [
                { nodeId: 1, sensorData: { temperature: 25, humidity: 60, light: 500, smoke: 20 } },
                { nodeId: 2, sensorData: { temperature: 26, humidity: 61, light: 501, smoke: 21 } },
                { nodeId: 3, sensorData: { temperature: 27, humidity: 62, light: 502, smoke: 22 } }
            ];

            const batchResult = this.adapter.batchToHuaweiIoTFormat(batchData);
            
            if (batchResult.success && batchResult.successCount === 3) {
                console.log('✅ 批量数据处理成功');
                this.testResults.push({
                    test: 'batch-data-processing',
                    status: 'passed',
                    message: '批量数据处理功能正常'
                });
            } else {
                throw new Error(`批量处理失败: 成功${batchResult.successCount}/3`);
            }
        } catch (error) {
            console.log('❌ 批量数据处理测试失败:', error.message);
            this.testResults.push({
                test: 'batch-data-processing',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试错误处理
    async testErrorHandling() {
        console.log('❌ 测试错误处理...');
        
        try {
            // 测试无效节点ID
            const invalidNodeResult = this.adapter.toHuaweiIoTFormat(999, {
                temperature: 25, humidity: 60, light: 500, smoke: 20
            });
            
            if (invalidNodeResult.success) {
                throw new Error('应该拒绝无效节点ID');
            }

            // 测试无效传感器数据
            const invalidDataResult = this.adapter.toHuaweiIoTFormat(1, {
                temperature: 'invalid', humidity: 60, light: 500, smoke: 20
            });
            
            if (invalidDataResult.success) {
                throw new Error('应该拒绝无效传感器数据');
            }

            console.log('✅ 错误处理功能正常');
            this.testResults.push({
                test: 'error-handling',
                status: 'passed',
                message: '错误处理功能正常'
            });
        } catch (error) {
            console.log('❌ 错误处理测试失败:', error.message);
            this.testResults.push({
                test: 'error-handling',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 测试统计信息
    async testStatistics() {
        console.log('📊 测试统计信息...');
        
        try {
            const stats = this.adapter.getStatistics();
            
            if (stats && typeof stats.messageCount === 'number' && typeof stats.errorCount === 'number') {
                console.log('✅ 统计信息功能正常');
                this.testResults.push({
                    test: 'statistics',
                    status: 'passed',
                    message: '统计信息功能正常'
                });
            } else {
                throw new Error('统计信息格式错误');
            }
        } catch (error) {
            console.log('❌ 统计信息测试失败:', error.message);
            this.testResults.push({
                test: 'statistics',
                status: 'failed',
                message: error.message
            });
        }
    }

    // 清理资源
    cleanup() {
        console.log('🧹 清理测试资源...');
        
        if (this.mqttService) {
            this.mqttService.stop();
            this.mqttService = null;
        }
    }

    // 打印测试摘要
    printTestSummary() {
        console.log('\n📋 MQTT测试摘要:');
        
        const passed = this.testResults.filter(r => r.status === 'passed').length;
        const failed = this.testResults.filter(r => r.status === 'failed').length;
        
        console.log(`✅ 通过: ${passed}`);
        console.log(`❌ 失败: ${failed}`);
        console.log(`📊 总计: ${this.testResults.length}`);
        
        this.testResults.forEach(result => {
            const icon = result.status === 'passed' ? '✅' : '❌';
            console.log(`${icon} ${result.test}: ${result.message}`);
        });
    }
}

// 运行测试
const tester = new MQTTTester();
tester.runTests().then(() => {
    console.log('\n✨ MQTT华为云IoT集成测试完成！');
    process.exit(0);
}).catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
});
