{"version": 3, "sources": ["container/TreeContainer/OrderedMap.js", "../../src/container/TreeContainer/OrderedMap.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "__values", "o", "s", "m", "i", "TreeC<PERSON>r", "TreeIterator", "throwIteratorAccessError", "OrderedMapIterator", "_super", "node", "header", "container", "iteratorType", "_this", "defineProperty", "get", "_node", "_header", "self", "Proxy", "props", "_key", "_value", "set", "newValue", "enumerable", "configurable", "copy", "OrderedMap", "cmp", "enableIndex", "for<PERSON>ach", "el", "setElement", "_iterationFunc", "curNode", "_a", "undefined", "_left", "_right", "begin", "end", "rBegin", "rEnd", "front", "_length", "minNode", "back", "maxNode", "lowerBound", "key", "resNode", "_lowerBound", "_root", "upperBound", "_upperBound", "reverseLowerBound", "_reverseLowerBound", "reverseUpperBound", "_reverseUpperBound", "hint", "_set", "find", "_findElementNode", "getElement<PERSON>y<PERSON>ey", "union", "other"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;AACA,IAAIM,WAAY7C,QAAQA,KAAK6C,KAAa,SAASC;IAC/C,IAAIC,WAAWd,WAAW,cAAcA,OAAOC,UAAUc,IAAID,KAAKD,EAAEC,IAAIE,IAAI;IAC5E,IAAID,GAAG,OAAOA,EAAErC,KAAKmC;IACrB,IAAIA,YAAYA,EAAEJ,WAAW,UAAU,OAAO;QAC1Cb,MAAM;YACF,IAAIiB,KAAKG,KAAKH,EAAEJ,QAAQI,SAAS;YACjC,OAAO;gBAAEN,OAAOM,KAAKA,EAAEG;gBAAMV,OAAOO;;AACxC;;IAEJ,MAAM,IAAIlC,UAAUmC,IAAI,4BAA4B;AACxD;;OCpDOG,mBAAmB;;OACnBC,kBAAkB;;SAGhBC,gCAA0B;;AAEnC,IAAAC,qBAAA,SAAAC;IAAuCvD,UAAAsD,oBAAAC;IAErC,SAAAD,mBACEE,GACAC,GACAC,GACAC;QAJF,IAAAC,IAMEL,EAAA3C,KAAAX,MAAMuD,GAAMC,GAAQE,MAAa1D;QACjC2D,EAAKF,YAAYA;QD8Cb,OAAOE;AACX;IC7CFvD,OAAAwD,eAAIP,mBAAA5C,WAAA,WAAO;QD+CLoD,KC/CN;YACE,IAAI7D,KAAK8D,MAAU9D,KAAK+D,GAAS;gBAC/BX;ADgDM;YC9CR,IAAMY,IAAOhE;YACb,OAAO,IAAIiE,MAAuB,IAAI;gBACpCJ,KAAA,SAAIzC,GAAG8C;oBACL,IAAIA,MAAU,KAAK,OAAOF,EAAKF,EAAMK,QAChC,IAAID,MAAU,KAAK,OAAOF,EAAKF,EAAMM;ADkDlC;gBChDVC,KAAA,SAAIjD,GAAG8C,GAAYI;oBACjB,IAAIJ,MAAU,KAAK;wBACjB,MAAM,IAAItD,UAAU;ADkDV;oBChDZoD,EAAKF,EAAMM,IAASE;oBACpB,OAAO;ADkDC;;AAER;QACAC,YAAY;QACZC,cAAc;;IClDpBnB,mBAAA5C,UAAAgE,OAAA;QACE,OAAO,IAAIpB,mBACTrD,KAAK8D,GACL9D,KAAK+D,GACL/D,KAAKyD,WACLzD,KAAK0D;ADiDP;IC5CJ,OAAAL;AAAA,CAxCA,CAAuCF;;AA4CvC,IAAAuB,aAAA,SAAApB;IAA+BvD,UAAA2E,YAAApB;IAW7B,SAAAoB,WACEjB,GACAkB,GACAC;QAFA,IAAAnB,WAAA,GAAA;YAAAA,IAAA;AAAqC;QADvC,IAAAE,IAKEL,EAAA3C,KAAAX,MAAM2E,GAAKC,MAAY5E;QACvB,IAAMgE,IAAOL;QACbF,EAAUoB,SAAQ,SAAUC;YAC1Bd,EAAKe,WAAWD,EAAG,IAAIA,EAAG;AD0CxB;QACA,OAAOnB;AACX;ICtCQe,WAAAjE,UAAAuE,IAAV,SACEC;QD0CI,OAAOhE,YAAYjB,OAAM,SAAUkF;YAC/B,QAAQA,EAAG7D;cACP,KAAK;gBC1CjB,IAAI4D,MAAYE,WAAW,OAAA,EAAA;gBAC3B,OAAA,EAAA,GAAAtC,SAAQ7C,KAAKgF,EAAeC,EAAQG;;cD6CxB,KAAK;gBC7CjBF,EAAA5D;gBACA,OAAA,EAAA,GAAc,EAAC2D,EAAQd,GAAMc,EAAQb;;cD+CzB,KAAK;gBC/CjBc,EAAA5D;gBACA,OAAA,EAAA,GAAAuB,SAAQ7C,KAAKgF,EAAeC,EAAQI;;cDiDxB,KAAK;gBCjDjBH,EAAA5D;gBDmDgB,OAAO,EAAC;;AAEpB;AACJ;ICpDFoD,WAAAjE,UAAA6E,QAAA;QACE,OAAO,IAAIjC,mBAAyBrD,KAAK+D,EAAQqB,KAASpF,KAAK+D,GAAS/D,KAAK+D,GAAS/D;ADsDtF;ICpDF0E,WAAAjE,UAAA8E,MAAA;QACE,OAAO,IAAIlC,mBAAyBrD,KAAK+D,GAAS/D,KAAK+D,GAAS/D;ADsDhE;ICpDF0E,WAAAjE,UAAA+E,SAAA;QACE,OAAO,IAAInC,mBACTrD,KAAK+D,EAAQsB,KAAUrF,KAAK+D,GAC5B/D,KAAK+D,GACL/D,MAAI;ADmDN;IC/CF0E,WAAAjE,UAAAgF,OAAA;QACE,OAAO,IAAIpC,mBAAyBrD,KAAK+D,GAAS/D,KAAK+D,GAAS/D,MAAI;ADiDpE;IC/CF0E,WAAAjE,UAAAiF,QAAA;QACE,IAAI1F,KAAK2F,MAAY,GAAG;QACxB,IAAMC,IAAU5F,KAAK+D,EAAQqB;QAC7B,OAAe,EAACQ,EAAQzB,GAAMyB,EAAQxB;ADkDtC;IChDFM,WAAAjE,UAAAoF,OAAA;QACE,IAAI7F,KAAK2F,MAAY,GAAG;QACxB,IAAMG,IAAU9F,KAAK+D,EAAQsB;QAC7B,OAAe,EAACS,EAAQ3B,GAAM2B,EAAQ1B;ADmDtC;ICjDFM,WAAAjE,UAAAsF,aAAA,SAAWC;QACT,IAAMC,IAAUjG,KAAKkG,EAAYlG,KAAKmG,GAAOH;QAC7C,OAAO,IAAI3C,mBAAyB4C,GAASjG,KAAK+D,GAAS/D;ADmD3D;ICjDF0E,WAAAjE,UAAA2F,aAAA,SAAWJ;QACT,IAAMC,IAAUjG,KAAKqG,EAAYrG,KAAKmG,GAAOH;QAC7C,OAAO,IAAI3C,mBAAyB4C,GAASjG,KAAK+D,GAAS/D;ADmD3D;ICjDF0E,WAAAjE,UAAA6F,oBAAA,SAAkBN;QAChB,IAAMC,IAAUjG,KAAKuG,EAAmBvG,KAAKmG,GAAOH;QACpD,OAAO,IAAI3C,mBAAyB4C,GAASjG,KAAK+D,GAAS/D;ADmD3D;ICjDF0E,WAAAjE,UAAA+F,oBAAA,SAAkBR;QAChB,IAAMC,IAAUjG,KAAKyG,EAAmBzG,KAAKmG,GAAOH;QACpD,OAAO,IAAI3C,mBAAyB4C,GAASjG,KAAK+D,GAAS/D;ADmD3D;ICrCF0E,WAAAjE,UAAAsE,aAAA,SAAWiB,GAAQxD,GAAUkE;QAC3B,OAAO1G,KAAK2G,EAAKX,GAAKxD,GAAOkE;ADmD7B;ICjDFhC,WAAAjE,UAAAmG,OAAA,SAAKZ;QACH,IAAMf,IAAUjF,KAAK6G,EAAiB7G,KAAKmG,GAAOH;QAClD,OAAO,IAAI3C,mBAAyB4B,GAASjF,KAAK+D,GAAS/D;ADmD3D;IC3CF0E,WAAAjE,UAAAqG,kBAAA,SAAgBd;QACd,IAAMf,IAAUjF,KAAK6G,EAAiB7G,KAAKmG,GAAOH;QAClD,OAAOf,EAAQb;ADmDf;ICjDFM,WAAAjE,UAAAsG,QAAA,SAAMC;QACJ,IAAMhD,IAAOhE;QACbgH,EAAMnC,SAAQ,SAAUC;YACtBd,EAAKe,WAAWD,EAAG,IAAIA,EAAG;ADmDxB;QCjDJ,OAAO9E,KAAK2F;ADmDZ;ICjDFjB,WAAAjE,UAACwB,OAAOC,YAAR;QACE,OAAOlC,KAAKgF,EAAehF,KAAKmG;ADmDhC;IC3CJ,OAAAzB;AAAA,CAzHA,CAA+BxB;;eA2HhBwB", "file": "OrderedMap.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nvar OrderedMapIterator = /** @class */ (function (_super) {\n    __extends(OrderedMapIterator, _super);\n    function OrderedMapIterator(node, header, container, iteratorType) {\n        var _this = _super.call(this, node, header, iteratorType) || this;\n        _this.container = container;\n        return _this;\n    }\n    Object.defineProperty(OrderedMapIterator.prototype, \"pointer\", {\n        get: function () {\n            if (this._node === this._header) {\n                throwIteratorAccessError();\n            }\n            var self = this;\n            return new Proxy([], {\n                get: function (_, props) {\n                    if (props === '0')\n                        return self._node._key;\n                    else if (props === '1')\n                        return self._node._value;\n                },\n                set: function (_, props, newValue) {\n                    if (props !== '1') {\n                        throw new TypeError('props must be 1');\n                    }\n                    self._node._value = newValue;\n                    return true;\n                }\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OrderedMapIterator.prototype.copy = function () {\n        return new OrderedMapIterator(this._node, this._header, this.container, this.iteratorType);\n    };\n    return OrderedMapIterator;\n}(TreeIterator));\nvar OrderedMap = /** @class */ (function (_super) {\n    __extends(OrderedMap, _super);\n    /**\n     * @param container - The initialization container.\n     * @param cmp - The compare function.\n     * @param enableIndex - Whether to enable iterator indexing function.\n     * @example\n     * new OrderedMap();\n     * new OrderedMap([[0, 1], [2, 1]]);\n     * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n     * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n     */\n    function OrderedMap(container, cmp, enableIndex) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this, cmp, enableIndex) || this;\n        var self = _this;\n        container.forEach(function (el) {\n            self.setElement(el[0], el[1]);\n        });\n        return _this;\n    }\n    /**\n     * @internal\n     */\n    OrderedMap.prototype._iterationFunc = function (curNode) {\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    if (curNode === undefined)\n                        return [2 /*return*/];\n                    return [5 /*yield**/, __values(this._iterationFunc(curNode._left))];\n                case 1:\n                    _a.sent();\n                    return [4 /*yield*/, [curNode._key, curNode._value]];\n                case 2:\n                    _a.sent();\n                    return [5 /*yield**/, __values(this._iterationFunc(curNode._right))];\n                case 3:\n                    _a.sent();\n                    return [2 /*return*/];\n            }\n        });\n    };\n    OrderedMap.prototype.begin = function () {\n        return new OrderedMapIterator(this._header._left || this._header, this._header, this);\n    };\n    OrderedMap.prototype.end = function () {\n        return new OrderedMapIterator(this._header, this._header, this);\n    };\n    OrderedMap.prototype.rBegin = function () {\n        return new OrderedMapIterator(this._header._right || this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    OrderedMap.prototype.rEnd = function () {\n        return new OrderedMapIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    OrderedMap.prototype.front = function () {\n        if (this._length === 0)\n            return;\n        var minNode = this._header._left;\n        return [minNode._key, minNode._value];\n    };\n    OrderedMap.prototype.back = function () {\n        if (this._length === 0)\n            return;\n        var maxNode = this._header._right;\n        return [maxNode._key, maxNode._value];\n    };\n    OrderedMap.prototype.lowerBound = function (key) {\n        var resNode = this._lowerBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    };\n    OrderedMap.prototype.upperBound = function (key) {\n        var resNode = this._upperBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    };\n    OrderedMap.prototype.reverseLowerBound = function (key) {\n        var resNode = this._reverseLowerBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    };\n    OrderedMap.prototype.reverseUpperBound = function (key) {\n        var resNode = this._reverseUpperBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    };\n    /**\n     * @description Insert a key-value pair or set value by the given key.\n     * @param key - The key want to insert.\n     * @param value - The value want to set.\n     * @param hint - You can give an iterator hint to improve insertion efficiency.\n     * @return The size of container after setting.\n     * @example\n     * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n     * const iter = mp.begin();\n     * mp.setElement(1, 0);\n     * mp.setElement(3, 0, iter);  // give a hint will be faster.\n     */\n    OrderedMap.prototype.setElement = function (key, value, hint) {\n        return this._set(key, value, hint);\n    };\n    OrderedMap.prototype.find = function (key) {\n        var curNode = this._findElementNode(this._root, key);\n        return new OrderedMapIterator(curNode, this._header, this);\n    };\n    /**\n     * @description Get the value of the element of the specified key.\n     * @param key - The specified key you want to get.\n     * @example\n     * const val = container.getElementByKey(1);\n     */\n    OrderedMap.prototype.getElementByKey = function (key) {\n        var curNode = this._findElementNode(this._root, key);\n        return curNode._value;\n    };\n    OrderedMap.prototype.union = function (other) {\n        var self = this;\n        other.forEach(function (el) {\n            self.setElement(el[0], el[1]);\n        });\n        return this._length;\n    };\n    OrderedMap.prototype[Symbol.iterator] = function () {\n        return this._iterationFunc(this._root);\n    };\n    return OrderedMap;\n}(TreeContainer));\nexport default OrderedMap;\n", "import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { TreeNode } from './Base/TreeNode';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass OrderedMapIterator<K, V> extends TreeIterator<K, V> {\n  container: OrderedMap<K, V>;\n  constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    container: OrderedMap<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy(<[K, V]><unknown>[], {\n      get(_, props: '0' | '1') {\n        if (props === '0') return self._node._key;\n        else if (props === '1') return self._node._value;\n      },\n      set(_, props: '1', newValue: V) {\n        if (props !== '1') {\n          throw new TypeError('props must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new OrderedMapIterator<K, V>(\n      this._node,\n      this._header,\n      this.container,\n      this.iteratorType\n    );\n  }\n  // @ts-ignore\n  equals(iter: OrderedMapIterator<K, V>): boolean;\n}\n\nexport type { OrderedMapIterator };\n\nclass OrderedMap<K, V> extends TreeContainer<K, V> {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedMap();\n   * new OrderedMap([[0, 1], [2, 1]]);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n   */\n  constructor(\n    container: initContainer<[K, V]> = [],\n    cmp?: (x: K, y: K) => number,\n    enableIndex?: boolean\n  ) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  /**\n   * @internal\n   */\n  private * _iterationFunc(\n    curNode: TreeNode<K, V> | undefined\n  ): Generator<[K, V], void> {\n    if (curNode === undefined) return;\n    yield * this._iterationFunc(curNode._left);\n    yield <[K, V]>[curNode._key, curNode._value];\n    yield * this._iterationFunc(curNode._right);\n  }\n  begin() {\n    return new OrderedMapIterator<K, V>(this._header._left || this._header, this._header, this);\n  }\n  end() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedMapIterator<K, V>(\n      this._header._right || this._header,\n      this._header,\n      this,\n      IteratorType.REVERSE\n    );\n  }\n  rEnd() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    if (this._length === 0) return;\n    const minNode = this._header._left!;\n    return <[K, V]>[minNode._key, minNode._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    const maxNode = this._header._right!;\n    return <[K, V]>[maxNode._key, maxNode._value];\n  }\n  lowerBound(key: K) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  upperBound(key: K) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseLowerBound(key: K) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseUpperBound(key: K) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n   * const iter = mp.begin();\n   * mp.setElement(1, 0);\n   * mp.setElement(3, 0, iter);  // give a hint will be faster.\n   */\n  setElement(key: K, value: V, hint?: OrderedMapIterator<K, V>) {\n    return this._set(key, value, hint);\n  }\n  find(key: K) {\n    const curNode = this._findElementNode(this._root, key);\n    return new OrderedMapIterator<K, V>(curNode, this._header, this);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The specified key you want to get.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key: K) {\n    const curNode = this._findElementNode(this._root, key);\n    return curNode._value;\n  }\n  union(other: OrderedMap<K, V>) {\n    const self = this;\n    other.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return this._length;\n  }\n  [Symbol.iterator]() {\n    return this._iterationFunc(this._root);\n  }\n  // @ts-ignore\n  eraseElementByIterator(iter: OrderedMapIterator<K, V>): OrderedMapIterator<K, V>;\n  // @ts-ignore\n  forEach(callback: (element: [K, V], index: number, map: OrderedMap<K, V>) => void): void;\n  // @ts-ignore\n  getElementByPos(pos: number): [K, V];\n}\n\nexport default OrderedMap;\n"]}