// 系统集成测试脚本
const http = require('http');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

class IntegrationTester {
    constructor() {
        this.baseUrl = 'http://127.0.0.1:3000';
        this.wsUrl = 'ws://127.0.0.1:3000';
        this.testResults = [];
        this.startTime = Date.now();
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🚀 开始系统集成测试...\n');
        
        try {
            // 1. 功能测试
            await this.runFunctionalTests();
            
            // 2. 性能测试
            await this.runPerformanceTests();
            
            // 3. API测试
            await this.runAPITests();
            
            // 4. WebSocket测试
            await this.runWebSocketTests();
            
            // 5. 前端资源测试
            await this.runFrontendTests();
            
            // 生成测试报告
            this.generateTestReport();
            
        } catch (error) {
            console.error('❌ 测试执行失败:', error);
            this.addTestResult('系统测试', false, error.message);
        }
    }

    // 功能测试
    async runFunctionalTests() {
        console.log('📋 执行功能测试...');
        
        // 测试主页面加载
        const homePageTest = await this.testHttpRequest('GET', '/');
        this.addTestResult('主页面加载', homePageTest.success, homePageTest.message);
        
        // 测试节点详情页面
        const nodeDetailTest = await this.testHttpRequest('GET', '/pages/node-detail.html?nodeId=1');
        this.addTestResult('节点详情页面', nodeDetailTest.success, nodeDetailTest.message);
        
        // 测试警告设置页面
        const alertSettingsTest = await this.testHttpRequest('GET', '/pages/alert-settings.html');
        this.addTestResult('警告设置页面', alertSettingsTest.success, alertSettingsTest.message);
        
        // 测试静态资源
        const cssTest = await this.testHttpRequest('GET', '/css/styles.css');
        this.addTestResult('CSS资源加载', cssTest.success, cssTest.message);
        
        const jsTest = await this.testHttpRequest('GET', '/js/config.js');
        this.addTestResult('JavaScript资源加载', jsTest.success, jsTest.message);
        
        console.log('✅ 功能测试完成\n');
    }

    // 性能测试
    async runPerformanceTests() {
        console.log('⚡ 执行性能测试...');
        
        // 响应时间测试
        const responseTimeTest = await this.testResponseTime();
        this.addTestResult('响应时间测试', responseTimeTest.success, responseTimeTest.message);
        
        // 并发连接测试
        const concurrencyTest = await this.testConcurrentConnections();
        this.addTestResult('并发连接测试', concurrencyTest.success, concurrencyTest.message);
        
        // 内存使用测试
        const memoryTest = await this.testMemoryUsage();
        this.addTestResult('内存使用测试', memoryTest.success, memoryTest.message);
        
        console.log('✅ 性能测试完成\n');
    }

    // API测试
    async runAPITests() {
        console.log('🔌 执行API测试...');
        
        // 测试健康检查端点
        const healthTest = await this.testHttpRequest('GET', '/api/health');
        this.addTestResult('健康检查API', healthTest.success, healthTest.message);
        
        // 测试节点数据端点
        const nodesTest = await this.testHttpRequest('GET', '/api/nodes');
        this.addTestResult('节点数据API', nodesTest.success, nodesTest.message);
        
        // 测试系统状态端点
        const statusTest = await this.testHttpRequest('GET', '/api/status');
        this.addTestResult('系统状态API', statusTest.success, statusTest.message);
        
        console.log('✅ API测试完成\n');
    }

    // WebSocket测试
    async runWebSocketTests() {
        console.log('🔗 执行WebSocket测试...');
        
        const wsTest = await this.testWebSocketConnection();
        this.addTestResult('WebSocket连接', wsTest.success, wsTest.message);
        
        console.log('✅ WebSocket测试完成\n');
    }

    // 前端资源测试
    async runFrontendTests() {
        console.log('🎨 执行前端资源测试...');
        
        // 测试关键JavaScript文件
        const jsFiles = [
            '/js/config.js',
            '/js/utils.js',
            '/js/app.js',
            '/js/websocket.js',
            '/js/nodes.js',
            '/js/alerts.js',
            '/js/charts.js'
        ];
        
        for (const file of jsFiles) {
            const test = await this.testHttpRequest('GET', file);
            this.addTestResult(`JS文件: ${file}`, test.success, test.message);
        }
        
        // 测试CSS文件
        const cssFiles = [
            '/css/styles.css',
            '/css/components.css'
        ];
        
        for (const file of cssFiles) {
            const test = await this.testHttpRequest('GET', file);
            this.addTestResult(`CSS文件: ${file}`, test.success, test.message);
        }
        
        console.log('✅ 前端资源测试完成\n');
    }

    // HTTP请求测试
    async testHttpRequest(method, path) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            const options = {
                hostname: '127.0.0.1',
                port: 3000,
                path: path,
                method: method,
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                const responseTime = Date.now() - startTime;
                
                if (res.statusCode >= 200 && res.statusCode < 400) {
                    resolve({
                        success: true,
                        message: `状态码: ${res.statusCode}, 响应时间: ${responseTime}ms`
                    });
                } else {
                    resolve({
                        success: false,
                        message: `状态码: ${res.statusCode}, 响应时间: ${responseTime}ms`
                    });
                }
            });

            req.on('error', (error) => {
                resolve({
                    success: false,
                    message: `请求失败: ${error.message}`
                });
            });

            req.on('timeout', () => {
                resolve({
                    success: false,
                    message: '请求超时'
                });
            });

            req.end();
        });
    }

    // 响应时间测试
    async testResponseTime() {
        const tests = [];
        const iterations = 10;
        
        for (let i = 0; i < iterations; i++) {
            const startTime = Date.now();
            const result = await this.testHttpRequest('GET', '/');
            const responseTime = Date.now() - startTime;
            
            if (result.success) {
                tests.push(responseTime);
            }
        }
        
        if (tests.length > 0) {
            const avgResponseTime = tests.reduce((sum, time) => sum + time, 0) / tests.length;
            const success = avgResponseTime < 1000; // 1秒内为合格
            
            return {
                success,
                message: `平均响应时间: ${avgResponseTime.toFixed(2)}ms (${iterations}次测试)`
            };
        } else {
            return {
                success: false,
                message: '所有响应时间测试失败'
            };
        }
    }

    // 并发连接测试
    async testConcurrentConnections() {
        const concurrentRequests = 20;
        const promises = [];
        
        for (let i = 0; i < concurrentRequests; i++) {
            promises.push(this.testHttpRequest('GET', '/'));
        }
        
        try {
            const results = await Promise.all(promises);
            const successCount = results.filter(r => r.success).length;
            const successRate = (successCount / concurrentRequests) * 100;
            
            return {
                success: successRate >= 90, // 90%成功率为合格
                message: `并发${concurrentRequests}个请求，成功率: ${successRate.toFixed(1)}%`
            };
        } catch (error) {
            return {
                success: false,
                message: `并发测试失败: ${error.message}`
            };
        }
    }

    // 内存使用测试
    async testMemoryUsage() {
        const memUsage = process.memoryUsage();
        const memUsageMB = {
            rss: Math.round(memUsage.rss / 1024 / 1024),
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024)
        };
        
        // 内存使用合理性检查（堆内存使用小于100MB为合格）
        const success = memUsageMB.heapUsed < 100;
        
        return {
            success,
            message: `堆内存使用: ${memUsageMB.heapUsed}MB, RSS: ${memUsageMB.rss}MB`
        };
    }

    // WebSocket连接测试
    async testWebSocketConnection() {
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket('ws://127.0.0.1:3000');
                let connected = false;
                
                const timeout = setTimeout(() => {
                    if (!connected) {
                        ws.close();
                        resolve({
                            success: false,
                            message: 'WebSocket连接超时'
                        });
                    }
                }, 5000);
                
                ws.on('open', () => {
                    connected = true;
                    clearTimeout(timeout);
                    ws.close();
                    resolve({
                        success: true,
                        message: 'WebSocket连接成功'
                    });
                });
                
                ws.on('error', (error) => {
                    clearTimeout(timeout);
                    resolve({
                        success: false,
                        message: `WebSocket连接失败: ${error.message}`
                    });
                });
                
            } catch (error) {
                resolve({
                    success: false,
                    message: `WebSocket测试异常: ${error.message}`
                });
            }
        });
    }

    // 添加测试结果
    addTestResult(testName, success, message) {
        this.testResults.push({
            name: testName,
            success,
            message,
            timestamp: new Date().toISOString()
        });
        
        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    // 生成测试报告
    generateTestReport() {
        const endTime = Date.now();
        const totalTime = endTime - this.startTime;
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const successRate = (passedTests / totalTests) * 100;
        
        const report = {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                successRate: successRate.toFixed(1) + '%',
                totalTime: totalTime + 'ms',
                timestamp: new Date().toISOString()
            },
            results: this.testResults
        };
        
        // 保存测试报告
        const reportPath = path.join(__dirname, '../docs/testing-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 输出测试摘要
        console.log('\n📊 测试摘要:');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests}`);
        console.log(`失败: ${failedTests}`);
        console.log(`成功率: ${successRate.toFixed(1)}%`);
        console.log(`总耗时: ${totalTime}ms`);
        console.log(`报告已保存: ${reportPath}`);
        
        if (successRate >= 90) {
            console.log('\n🎉 系统集成测试通过！');
        } else {
            console.log('\n⚠️ 系统集成测试存在问题，请检查失败的测试项。');
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new IntegrationTester();
    tester.runAllTests().catch(console.error);
}

module.exports = IntegrationTester;
