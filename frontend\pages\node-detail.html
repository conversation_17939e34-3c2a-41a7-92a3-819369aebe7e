<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点详情 - 龙芯智能终端管理系统</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/node-detail.css">
</head>
<body>
    <!-- 加载屏幕 -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="logo-icon">🖥️</div>
                <h2>加载节点详情...</h2>
            </div>
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
            </div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="app-container" id="appContainer">
        <!-- 头部 -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <div class="header-left">
                        <button class="back-button" id="backButton">
                            <span class="back-icon">←</span>
                            <span>返回</span>
                        </button>
                        <h1 class="page-title" id="pageTitle">节点详情</h1>
                    </div>
                    <div class="header-right">
                        <div class="theme-toggle" id="themeToggle">
                            <span class="theme-icon">🌙</span>
                        </div>
                        <div class="time-display" id="currentTime"></div>
                        <div class="connection-status" id="connectionStatus">
                            <span class="status-dot"></span>
                            <span class="status-text">连接中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容 -->
        <main class="main-content">
            <div class="container">
                <!-- 节点概览卡片 -->
                <section class="node-overview">
                    <div class="node-overview-card">
                        <div class="node-header">
                            <div class="node-info">
                                <h2 class="node-name" id="nodeName">节点一</h2>
                                <div class="node-status" id="nodeStatus">
                                    <span class="status-dot"></span>
                                    <span class="status-text">离线</span>
                                </div>
                            </div>
                            <div class="node-actions">
                                <button class="action-btn" id="resetNodeBtn">重置</button>
                                <button class="action-btn" id="configNodeBtn">配置</button>
                                <button class="action-btn" id="exportDataBtn">导出</button>
                            </div>
                        </div>
                        
                        <div class="node-stats">
                            <div class="stat-item">
                                <span class="stat-label">最后更新</span>
                                <span class="stat-value" id="lastUpdate">从未</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">数据点数</span>
                                <span class="stat-value" id="dataPoints">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">警告数量</span>
                                <span class="stat-value" id="alertCount">0</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 实时数据区域 -->
                <section class="realtime-data">
                    <h3 class="section-title">实时传感器数据</h3>
                    <div class="sensor-cards-grid">
                        <div class="sensor-card" data-sensor="temperature">
                            <div class="sensor-icon">🌡️</div>
                            <div class="sensor-info">
                                <h4 class="sensor-name">温度</h4>
                                <div class="sensor-value" id="temperatureValue">0°C</div>
                                <div class="sensor-status normal" id="temperatureStatus">正常</div>
                            </div>
                        </div>
                        
                        <div class="sensor-card" data-sensor="humidity">
                            <div class="sensor-icon">💧</div>
                            <div class="sensor-info">
                                <h4 class="sensor-name">湿度</h4>
                                <div class="sensor-value" id="humidityValue">0%RH</div>
                                <div class="sensor-status normal" id="humidityStatus">正常</div>
                            </div>
                        </div>
                        
                        <div class="sensor-card" data-sensor="light">
                            <div class="sensor-icon">💡</div>
                            <div class="sensor-info">
                                <h4 class="sensor-name">光照</h4>
                                <div class="sensor-value" id="lightValue">0lm</div>
                                <div class="sensor-status normal" id="lightStatus">正常</div>
                            </div>
                        </div>
                        
                        <div class="sensor-card" data-sensor="smoke">
                            <div class="sensor-icon">💨</div>
                            <div class="sensor-info">
                                <h4 class="sensor-name">烟雾</h4>
                                <div class="sensor-value" id="smokeValue">0mg</div>
                                <div class="sensor-status normal" id="smokeStatus">正常</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 历史数据图表 -->
                <section class="history-charts">
                    <h3 class="section-title">历史数据趋势</h3>
                    <div class="chart-controls">
                        <button class="chart-tab active" data-chart="temperature">温度</button>
                        <button class="chart-tab" data-chart="humidity">湿度</button>
                        <button class="chart-tab" data-chart="light">光照</button>
                        <button class="chart-tab" data-chart="smoke">烟雾</button>
                    </div>
                    <div class="chart-container">
                        <canvas id="nodeChart"></canvas>
                    </div>
                </section>

                <!-- 警告日志 -->
                <section class="alert-logs">
                    <h3 class="section-title">警告日志</h3>
                    <div class="alert-filters">
                        <button class="filter-btn active" data-level="all">全部</button>
                        <button class="filter-btn" data-level="error">错误</button>
                        <button class="filter-btn" data-level="warning">警告</button>
                        <button class="filter-btn" data-level="info">信息</button>
                    </div>
                    <div class="alert-list" id="alertList">
                        <!-- 警告项将动态插入 -->
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 第三方库 -->
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.7.2/dist/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <!-- JavaScript -->
    <script src="../js/config.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/websocket.js"></script>
    <script src="../js/chartUtils.js"></script>
    <script src="../js/dataExport.js"></script>
    <script src="../js/node-detail.js"></script>
</body>
</html>
