// 内存数据存储管理器 - 管理节点和传感器数据
const NodeInfo = require('../models/NodeInfo');
const SensorData = require('../models/SensorData');
const AlertConfig = require('../models/AlertConfig');
const DataValidator = require('../utils/dataValidator');

class DataManager {
    constructor() {
        this.nodes = new Map(); // 存储节点信息
        this.alertConfigs = new Map(); // 存储警告配置
        this.systemStatus = {
            startTime: new Date(),
            totalDataPoints: 0,
            totalAlerts: 0,
            lastUpdate: null
        };
        
        // 初始化三个节点
        this.initializeNodes();
    }

    // 初始化节点 - 复用Qt项目的三节点设置
    initializeNodes() {
        for (let i = 1; i <= 3; i++) {
            const node = NodeInfo.create(i, `节点${i}`);
            this.nodes.set(i, node);
            
            // 创建默认警告配置
            const alertConfig = AlertConfig.create(i);
            this.alertConfigs.set(i, alertConfig);
        }
        
        console.log('✅ Initialized 3 nodes with default configurations');
    }

    // 获取所有节点信息
    getAllNodes() {
        const nodes = [];
        for (const [id, node] of this.nodes) {
            nodes.push(node.toJSON());
        }
        return nodes.sort((a, b) => a.id - b.id);
    }

    // 获取单个节点信息
    getNode(nodeId) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        const node = this.nodes.get(validation.value);
        return node ? node.toJSON() : null;
    }

    // 获取节点当前数据
    getNodeCurrentData(nodeId) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        const node = this.nodes.get(validation.value);
        return node && node.currentData ? node.currentData.toJSON() : null;
    }

    // 获取节点历史数据
    getNodeHistory(nodeId, hours = 1) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        const node = this.nodes.get(validation.value);
        if (!node) return [];
        
        return node.getHistory(hours).map(data => data.toJSON());
    }

    // 更新节点传感器数据
    updateNodeData(nodeId, sensorValues) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        // 验证传感器数据
        const dataValidation = DataValidator.validateSensorData(sensorValues);
        if (!dataValidation.isValid) {
            throw new Error(`Invalid sensor data: ${dataValidation.errors.join(', ')}`);
        }
        
        // 创建传感器数据对象
        const sensorData = SensorData.create(validation.value, sensorValues);
        
        // 获取节点
        const node = this.nodes.get(validation.value);
        if (!node) {
            throw new Error(`Node ${validation.value} not found`);
        }
        
        // 更新节点数据
        node.updateSensorData(sensorData);
        
        // 更新系统统计
        this.systemStatus.totalDataPoints++;
        this.systemStatus.lastUpdate = new Date();
        
        // 检查警告
        const alertConfig = this.alertConfigs.get(validation.value);
        if (alertConfig) {
            const alerts = alertConfig.checkThresholds(sensorData);
            if (alerts.length > 0) {
                this.systemStatus.totalAlerts += alerts.length;
            }
        }
        
        return sensorData.toJSON();
    }

    // 从Qt格式更新数据
    updateFromQtFormat(qtData) {
        try {
            const sensorData = SensorData.fromQtFormat(qtData);
            return this.updateNodeData(sensorData.nodeId, {
                temperature: sensorData.temperature,
                humidity: sensorData.humidity,
                light: sensorData.light,
                smoke: sensorData.smoke
            });
        } catch (error) {
            throw new Error(`Failed to process Qt data: ${error.message}`);
        }
    }

    // 获取节点警告配置
    getAlertConfig(nodeId) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        const config = this.alertConfigs.get(validation.value);
        return config ? config.toJSON() : null;
    }

    // 更新节点警告配置
    updateAlertConfig(nodeId, configData) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        // 验证配置数据
        const configValidation = DataValidator.validateAlertConfig(configData);
        if (!configValidation.isValid) {
            throw new Error(`Invalid alert config: ${configValidation.errors.join(', ')}`);
        }
        
        // 获取或创建配置
        let config = this.alertConfigs.get(validation.value);
        if (!config) {
            config = AlertConfig.create(validation.value);
            this.alertConfigs.set(validation.value, config);
        }
        
        // 更新配置
        if (configData.tempRange) {
            config.updateTempRange(configData.tempRange.min, configData.tempRange.max);
        }
        if (configData.humiRange) {
            config.updateHumiRange(configData.humiRange.min, configData.humiRange.max);
        }
        if (configData.lightRange) {
            config.updateLightRange(configData.lightRange.min, configData.lightRange.max);
        }
        if (configData.smokeMax !== undefined) {
            config.updateSmokeMax(configData.smokeMax);
        }
        if (configData.enabled !== undefined) {
            config.enabled = configData.enabled;
        }
        if (configData.notificationSettings) {
            config.updateNotificationSettings(configData.notificationSettings);
        }
        
        return config.toJSON();
    }

    // 获取所有活跃警告
    getAllActiveAlerts() {
        const alerts = [];
        for (const [nodeId, node] of this.nodes) {
            const nodeAlerts = node.getAlertHistory(1); // 最近1小时
            alerts.push(...nodeAlerts);
        }
        return alerts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }

    // 获取系统状态
    getSystemStatus() {
        const onlineNodes = Array.from(this.nodes.values()).filter(node => node.status).length;
        const totalNodes = this.nodes.size;
        
        return {
            ...this.systemStatus,
            nodes: {
                total: totalNodes,
                online: onlineNodes,
                offline: totalNodes - onlineNodes
            },
            uptime: Date.now() - this.systemStatus.startTime.getTime()
        };
    }

    // 重置节点数据
    resetNode(nodeId) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        const node = this.nodes.get(validation.value);
        if (node) {
            node.reset();
        }
        
        return { success: true, message: `Node ${validation.value} reset successfully` };
    }

    // 重置所有数据
    resetAll() {
        for (const [nodeId, node] of this.nodes) {
            node.reset();
        }
        
        this.systemStatus = {
            startTime: new Date(),
            totalDataPoints: 0,
            totalAlerts: 0,
            lastUpdate: null
        };
        
        return { success: true, message: 'All nodes reset successfully' };
    }

    // 获取数据统计
    getDataStatistics(hours = 24) {
        const stats = {
            totalDataPoints: 0,
            nodeStats: {},
            timeRange: hours
        };
        
        for (const [nodeId, node] of this.nodes) {
            const history = node.getHistory(hours);
            stats.nodeStats[nodeId] = {
                dataPoints: history.length,
                alerts: node.getAlertHistory(hours).length,
                lastUpdate: node.lastSeen ? node.lastSeen.toISOString() : null,
                status: node.status
            };
            stats.totalDataPoints += history.length;
        }
        
        return stats;
    }
}

// 创建单例实例
const dataManager = new DataManager();

module.exports = dataManager;
