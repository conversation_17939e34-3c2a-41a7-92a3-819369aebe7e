// 主应用逻辑 - 复用Qt项目控制逻辑
class App {
    constructor() {
        this.isInitialized = false;
        this.loadingScreen = null;
        this.appContainer = null;
        this.currentTime = null;
        this.connectionStatus = null;
        this.themeToggle = null;
        
        // 应用状态
        this.state = {
            isConnected: false,
            nodes: [],
            systemStatus: null,
            alerts: [],
            theme: 'light'
        };

        // 管理器实例
        this.managers = {
            websocket: null,
            nodes: null,
            alerts: null,
            charts: null
        };

        // 初始化应用
        this.init();
    }

    // 初始化应用
    async init() {
        try {
            console.log('🚀 初始化龙芯终端管理系统...');
            
            // 显示加载屏幕
            this.showLoadingScreen();
            
            // 初始化DOM元素
            this.initDOMElements();
            
            // 初始化主题
            this.initTheme();
            
            // 初始化事件监听器
            this.initEventListeners();
            
            // 初始化时间显示
            this.initTimeDisplay();
            
            // 模拟加载过程
            await this.simulateLoading();
            
            // 初始化管理器
            await this.initManagers();

            // 隐藏加载屏幕
            this.hideLoadingScreen();

            // 标记为已初始化
            this.isInitialized = true;

            console.log('✅ 应用初始化完成');
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    }

    // 初始化DOM元素
    initDOMElements() {
        this.loadingScreen = Utils.dom.$('#loadingScreen');
        this.appContainer = Utils.dom.$('#appContainer');
        this.currentTime = Utils.dom.$('#currentTime');
        this.connectionStatus = Utils.dom.$('#connectionStatus');
        this.themeToggle = Utils.dom.$('#themeToggle');
        
        console.log('📱 DOM元素初始化完成');
    }

    // 初始化主题
    initTheme() {
        Utils.theme.init();
        this.state.theme = Utils.theme.get();
        console.log(`🎨 主题初始化完成: ${this.state.theme}`);
    }

    // 初始化事件监听器
    initEventListeners() {
        // 主题切换
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                const newTheme = Utils.theme.toggle();
                this.state.theme = newTheme;
                console.log(`🎨 主题切换为: ${newTheme}`);
            });
        }

        // 窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleResize();
        }, 250));

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        console.log('👂 事件监听器初始化完成');
    }

    // 初始化时间显示
    initTimeDisplay() {
        this.updateTime();
        setInterval(() => {
            this.updateTime();
        }, 1000);
        
        console.log('⏰ 时间显示初始化完成');
    }

    // 更新时间显示
    updateTime() {
        if (this.currentTime) {
            this.currentTime.textContent = Utils.formatTime();
        }
    }

    // 显示加载屏幕
    showLoadingScreen() {
        if (this.loadingScreen) {
            this.loadingScreen.style.display = 'flex';
            Utils.dom.removeClass(this.loadingScreen, 'hidden');
        }
        
        if (this.appContainer) {
            this.appContainer.style.display = 'none';
        }
    }

    // 隐藏加载屏幕
    hideLoadingScreen() {
        if (this.loadingScreen) {
            Utils.dom.addClass(this.loadingScreen, 'hidden');
            
            setTimeout(() => {
                this.loadingScreen.style.display = 'none';
            }, 500);
        }
        
        if (this.appContainer) {
            this.appContainer.style.display = 'flex';
            Utils.animation.fadeIn(this.appContainer, 500);
        }
    }

    // 模拟加载过程
    async simulateLoading() {
        const loadingText = Utils.dom.$('.loading-text');
        const steps = [
            '正在初始化系统...',
            '正在连接服务器...',
            '正在加载配置...',
            '正在初始化组件...',
            '系统就绪！'
        ];

        for (let i = 0; i < steps.length; i++) {
            if (loadingText) {
                loadingText.textContent = steps[i];
            }
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    }

    // 处理窗口大小变化
    handleResize() {
        console.log('📐 窗口大小变化');
        // 这里可以添加响应式处理逻辑
    }

    // 处理页面可见性变化
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('👁️ 页面隐藏');
        } else {
            console.log('👁️ 页面显示');
            this.updateTime(); // 页面重新显示时更新时间
        }
    }

    // 更新连接状态
    updateConnectionStatus(isConnected, statusText = '') {
        this.state.isConnected = isConnected;
        
        if (this.connectionStatus) {
            const statusDot = this.connectionStatus.querySelector('.status-dot');
            const statusTextElement = this.connectionStatus.querySelector('.status-text');
            
            if (statusDot) {
                Utils.dom.removeClass(statusDot, 'connected', 'disconnected', 'connecting');
                if (isConnected) {
                    Utils.dom.addClass(statusDot, 'connected');
                } else {
                    Utils.dom.addClass(statusDot, 'disconnected');
                }
            }
            
            if (statusTextElement) {
                statusTextElement.textContent = statusText || (isConnected ? '已连接' : '未连接');
            }
        }
        
        console.log(`🔗 连接状态更新: ${isConnected ? '已连接' : '未连接'}`);
    }

    // 显示错误信息
    showError(message) {
        console.error('❌ 错误:', message);
        // 这里可以添加错误提示UI
        alert(message); // 临时使用alert，后续可以改为更好的UI
    }

    // 显示成功信息
    showSuccess(message) {
        console.log('✅ 成功:', message);
        // 这里可以添加成功提示UI
    }

    // 获取应用状态
    getState() {
        return Utils.deepClone(this.state);
    }

    // 更新应用状态
    setState(newState) {
        this.state = { ...this.state, ...newState };
        console.log('📊 应用状态更新:', this.state);

        // 通知管理器状态变化
        this.notifyStateChange(newState);
    }

    // 初始化管理器
    async initManagers() {
        try {
            console.log('🔧 初始化管理器...');

            // 初始化节点管理器
            this.managers.nodes = new NodeManager();

            // 初始化警告管理器
            this.managers.alerts = new AlertManager();

            // 初始化图表管理器
            this.managers.charts = new ChartManager();

            // 初始化WebSocket管理器
            this.managers.websocket = new WebSocketManager();

            // 设置WebSocket事件监听
            this.setupWebSocketEvents();

            console.log('✅ 所有管理器初始化完成');

        } catch (error) {
            console.error('❌ 管理器初始化失败:', error);
            throw error;
        }
    }

    // 设置WebSocket事件监听
    setupWebSocketEvents() {
        const ws = this.managers.websocket;

        // 连接状态变化
        ws.on('connected', () => {
            this.updateConnectionStatus(true, '已连接');
        });

        ws.on('disconnected', () => {
            this.updateConnectionStatus(false, '连接断开');
        });

        // 数据更新
        ws.on('dataUpdate', (data) => {
            this.handleDataUpdate(data);
        });

        // 初始数据
        ws.on('initialData', (data) => {
            this.handleInitialData(data);
        });

        // 警告触发
        ws.on('alertTrigger', (data) => {
            this.handleAlertTrigger(data);
        });

        console.log('👂 WebSocket事件监听设置完成');
    }

    // 处理数据更新
    handleDataUpdate(data) {
        if (data.nodes && Array.isArray(data.nodes)) {
            // 更新节点数据
            this.managers.nodes.updateNodes(data.nodes);

            // 更新图表数据
            data.nodes.forEach(node => {
                if (node.currentData) {
                    this.managers.charts.updateChartData(node.id, node.currentData);

                    // 检查警告
                    this.managers.alerts.checkSensorData(node.id, node.currentData);
                }
            });

            // 更新应用状态
            this.setState({ nodes: data.nodes, lastUpdate: data.timestamp });
        }
    }

    // 处理初始数据
    handleInitialData(data) {
        console.log('📊 处理初始数据:', data);

        if (data.nodes) {
            this.managers.nodes.updateNodes(data.nodes);
            this.setState({ nodes: data.nodes });
        }

        if (data.systemStatus) {
            this.setState({ systemStatus: data.systemStatus });
        }

        if (data.alerts) {
            this.setState({ alerts: data.alerts });
        }
    }

    // 处理警告触发
    handleAlertTrigger(data) {
        if (data.alerts && Array.isArray(data.alerts)) {
            data.alerts.forEach(alert => {
                this.managers.alerts.addAlert(alert);
            });
        }
    }

    // 通知状态变化
    notifyStateChange(newState) {
        // 可以在这里通知其他组件状态变化
        const event = new CustomEvent('appStateChange', {
            detail: { newState, fullState: this.state }
        });
        document.dispatchEvent(event);
    }

    // 获取管理器
    getManager(type) {
        return this.managers[type];
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});
