// 数据模型测试 - 验证所有数据模型功能
const SensorData = require('../models/SensorData');
const NodeInfo = require('../models/NodeInfo');
const AlertConfig = require('../models/AlertConfig');
const DataValidator = require('../utils/dataValidator');
const dataManager = require('../services/dataManager');

console.log('🧪 开始数据模型测试...\n');

// 测试1: SensorData模型
console.log('📊 测试 SensorData 模型:');
try {
    // 创建有效的传感器数据
    const validData = SensorData.create(1, {
        temperature: 25.5,
        humidity: 60.2,
        light: 800,
        smoke: 15.3
    });
    console.log('✅ 创建有效传感器数据:', validData.toJSON());
    
    // 测试数据验证
    const validation = SensorData.validateRanges({
        temperature: 25.5,
        humidity: 60.2,
        light: 800,
        smoke: 15.3
    });
    console.log('✅ 数据验证通过:', validation.isValid);
    
    // 测试Qt格式转换
    const qtData = "1,800,25.5,60.2,15.3";
    const fromQt = SensorData.fromQtFormat(qtData);
    console.log('✅ Qt格式转换:', fromQt.toJSON());
    
    // 测试华为云格式
    const huaweiFormat = validData.toHuaweiIoTFormat();
    console.log('✅ 华为云格式:', JSON.stringify(huaweiFormat, null, 2));
    
    // 测试警告检查
    const alerts = validData.checkAlerts();
    console.log('✅ 警告检查:', alerts.length === 0 ? '无警告' : alerts);
    
} catch (error) {
    console.log('❌ SensorData测试失败:', error.message);
}

console.log('\n📍 测试 NodeInfo 模型:');
try {
    // 创建节点
    const node = NodeInfo.create(1, '测试节点1');
    console.log('✅ 创建节点:', node.getSummary());
    
    // 更新传感器数据
    const sensorData = SensorData.create(1, {
        temperature: 30.0,
        humidity: 70.0,
        light: 500,
        smoke: 20.0
    });
    node.updateSensorData(sensorData);
    console.log('✅ 更新传感器数据:', node.getSummary());
    
    // 测试离线检测
    console.log('✅ 离线检测:', node.isOffline() ? '离线' : '在线');
    
    // 获取历史数据
    const history = node.getHistory(1);
    console.log('✅ 历史数据数量:', history.length);
    
} catch (error) {
    console.log('❌ NodeInfo测试失败:', error.message);
}

console.log('\n⚠️ 测试 AlertConfig 模型:');
try {
    // 创建警告配置
    const alertConfig = AlertConfig.create(1);
    console.log('✅ 创建警告配置:', alertConfig.toJSON());
    
    // 更新温度范围
    alertConfig.updateTempRange(10, 40);
    console.log('✅ 更新温度范围:', alertConfig.tempRange);
    
    // 测试阈值检查
    const testData = SensorData.create(1, {
        temperature: 45.0, // 超出范围
        humidity: 60.0,
        light: 500,
        smoke: 20.0
    });
    const thresholdAlerts = alertConfig.checkThresholds(testData);
    console.log('✅ 阈值检查警告:', thresholdAlerts.length > 0 ? thresholdAlerts : '无警告');
    
    // 验证配置
    const configValidation = alertConfig.validate();
    console.log('✅ 配置验证:', configValidation.isValid ? '有效' : configValidation.errors);
    
} catch (error) {
    console.log('❌ AlertConfig测试失败:', error.message);
}

console.log('\n🔍 测试 DataValidator:');
try {
    // 测试节点ID验证
    const nodeIdTest = DataValidator.validateNodeId(1);
    console.log('✅ 节点ID验证:', nodeIdTest.isValid ? '有效' : nodeIdTest.error);
    
    // 测试传感器数据验证
    const sensorTest = DataValidator.validateSensorData({
        temperature: 25.0,
        humidity: 60.0,
        light: 500,
        smoke: 20.0
    });
    console.log('✅ 传感器数据验证:', sensorTest.isValid ? '有效' : sensorTest.errors);
    
    // 测试Qt格式验证
    const qtTest = DataValidator.validateQtFormat("1,500,25.0,60.0,20.0");
    console.log('✅ Qt格式验证:', qtTest.isValid ? '有效' : qtTest.error);
    
    // 测试数据清理
    const sanitized = DataValidator.sanitizeData({
        temperature: 25.123456,
        humidity: 60.987654,
        light: 500.789,
        smoke: 20.555
    });
    console.log('✅ 数据清理:', sanitized);
    
} catch (error) {
    console.log('❌ DataValidator测试失败:', error.message);
}

console.log('\n💾 测试 DataManager:');
try {
    // 获取所有节点
    const allNodes = dataManager.getAllNodes();
    console.log('✅ 获取所有节点数量:', allNodes.length);
    
    // 更新节点数据
    const updateResult = dataManager.updateNodeData(1, {
        temperature: 28.5,
        humidity: 65.0,
        light: 600,
        smoke: 25.0
    });
    console.log('✅ 更新节点数据:', updateResult);
    
    // 从Qt格式更新
    const qtUpdateResult = dataManager.updateFromQtFormat("2,700,30.0,70.0,30.0");
    console.log('✅ Qt格式更新:', qtUpdateResult);
    
    // 获取系统状态
    const systemStatus = dataManager.getSystemStatus();
    console.log('✅ 系统状态:', {
        totalNodes: systemStatus.nodes.total,
        onlineNodes: systemStatus.nodes.online,
        totalDataPoints: systemStatus.totalDataPoints
    });
    
    // 获取数据统计
    const stats = dataManager.getDataStatistics(1);
    console.log('✅ 数据统计:', {
        totalDataPoints: stats.totalDataPoints,
        nodeCount: Object.keys(stats.nodeStats).length
    });
    
} catch (error) {
    console.log('❌ DataManager测试失败:', error.message);
}

console.log('\n🎯 测试边界情况:');
try {
    // 测试无效节点ID
    try {
        SensorData.create(4, { temperature: 25, humidity: 60, light: 500, smoke: 20 });
        console.log('❌ 应该拒绝无效节点ID');
    } catch (e) {
        console.log('✅ 正确拒绝无效节点ID:', e.message);
    }
    
    // 测试超出范围的数据
    try {
        SensorData.create(1, { temperature: 100, humidity: 60, light: 500, smoke: 20 });
        console.log('❌ 应该拒绝超出范围的数据');
    } catch (e) {
        console.log('✅ 正确拒绝超出范围的数据:', e.message);
    }
    
    // 测试无效Qt格式
    try {
        SensorData.fromQtFormat("invalid,data");
        console.log('❌ 应该拒绝无效Qt格式');
    } catch (e) {
        console.log('✅ 正确拒绝无效Qt格式:', e.message);
    }
    
} catch (error) {
    console.log('❌ 边界测试失败:', error.message);
}

console.log('\n🎉 数据模型测试完成！');
console.log('📋 测试总结:');
console.log('- SensorData模型: 数据创建、验证、格式转换');
console.log('- NodeInfo模型: 节点管理、状态更新、历史记录');
console.log('- AlertConfig模型: 警告配置、阈值检查');
console.log('- DataValidator: 数据验证、格式检查');
console.log('- DataManager: 内存存储、数据管理');
console.log('- 边界情况: 错误处理、数据验证');
