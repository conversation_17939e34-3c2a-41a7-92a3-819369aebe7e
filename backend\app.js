const express = require('express');
const http = require('http');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');

// 导入中间件
const requestLogger = require('./middleware/requestLogger');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');

// 导入路由
const nodesRouter = require('./routes/nodes');
const alertsRouter = require('./routes/alerts');
const systemRouter = require('./routes/system');

// 导入WebSocket服务
const WebSocketService = require('./services/websocket');

// 导入MQTT服务
const MQTTService = require('./services/mqttService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3000;

// 请求日志中间件
app.use(requestLogger);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: false, // 允许内联脚本用于开发
    crossOriginEmbedderPolicy: false
}));

// CORS配置
app.use(cors({
    origin: ['http://127.0.0.1:3000', 'http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// 解析JSON请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 - 提供前端文件
app.use(express.static(path.join(__dirname, '../frontend')));

// 基础路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// API路由
app.use('/api/nodes', nodesRouter);
app.use('/api/alerts', alertsRouter);
app.use('/api/system', systemRouter);

// 保留原有的健康检查端点
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        message: 'Loong Terminal Web Server is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// 简化的状态端点（用于测试）
app.get('/api/status', (req, res) => {
    const memUsage = process.memoryUsage();
    res.json({
        status: 'running',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: {
            rss: Math.round(memUsage.rss / 1024 / 1024),
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024)
        }
    });
});

// API文档路由
app.get('/api', (req, res) => {
    res.json({
        name: 'Loong Terminal Web API',
        version: '1.0.0',
        description: '龙芯智能终端管理系统API接口',
        endpoints: {
            nodes: {
                'GET /api/nodes': '获取所有节点状态',
                'GET /api/nodes/:id': '获取单个节点信息',
                'GET /api/nodes/:id/data': '获取节点当前数据',
                'GET /api/nodes/:id/history': '获取节点历史数据',
                'POST /api/nodes/:id/data': '更新节点数据',
                'POST /api/nodes/qt-data': '接收Qt格式数据',
                'PUT /api/nodes/:id/reset': '重置节点数据'
            },
            alerts: {
                'GET /api/alerts': '获取所有警告',
                'GET /api/alerts/summary': '获取警告摘要',
                'GET /api/alerts/config/:nodeId': '获取警告配置',
                'POST /api/alerts/config/:nodeId': '更新警告配置',
                'POST /api/alerts/test/:nodeId': '测试警告配置'
            },
            system: {
                'GET /api/system/status': '获取系统状态',
                'GET /api/system/health': '健康检查',
                'GET /api/system/stats': '获取统计信息',
                'GET /api/system/info': '获取系统信息',
                'POST /api/system/reset': '重置系统数据'
            }
        }
    });
});

// 404处理
app.use(notFoundHandler);

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
server.listen(PORT, '127.0.0.1', () => {
    console.log('🚀 龙芯智能终端管理系统Web版启动成功！');
    console.log(`📊 仪表板: http://127.0.0.1:${PORT}`);
    console.log(`🔧 API文档: http://127.0.0.1:${PORT}/api`);
    console.log(`💚 健康检查: http://127.0.0.1:${PORT}/api/health`);
    console.log(`📈 系统状态: http://127.0.0.1:${PORT}/api/system/status`);
    console.log('✨ 系统就绪，等待连接...');
});

// 初始化WebSocket服务
const webSocketService = new WebSocketService(server);

// 初始化MQTT服务
const mqttService = new MQTTService();

// 启动MQTT服务（可选，根据配置决定）
mqttService.initialize().then(() => {
    console.log('📡 MQTT服务已初始化');
    // 可以根据配置决定是否自动启动
    // mqttService.start();
}).catch(error => {
    console.error('❌ MQTT服务初始化失败:', error.message);
});

module.exports = { app, server, webSocketService, mqttService };
