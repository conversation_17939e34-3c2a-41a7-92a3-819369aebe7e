// 华为云IoT数据适配器 - 数据格式转换和处理
const DataValidator = require('../utils/dataValidator');

class HuaweiIoTAdapter {
    constructor() {
        this.messageCount = 0;
        this.errorCount = 0;
        this.lastProcessTime = null;
    }

    // 将Web API数据转换为华为云IoT格式
    toHuaweiIoTFormat(nodeId, sensorData) {
        try {
            // 验证输入数据
            const nodeValidation = DataValidator.validateNodeId(nodeId);
            if (!nodeValidation.isValid) {
                throw new Error(nodeValidation.error);
            }

            const dataValidation = DataValidator.validateSensorData(sensorData);
            if (!dataValidation.isValid) {
                throw new Error(`Invalid sensor data: ${dataValidation.errors.join(', ')}`);
            }

            // 构建华为云IoT标准格式
            const huaweiMessage = {
                services: [{
                    service_id: "SensorData",
                    properties: {
                        nodeId: nodeValidation.value,
                        temperature: Number(sensorData.temperature.toFixed(1)),
                        humidity: Number(sensorData.humidity.toFixed(1)),
                        light: Number(sensorData.light.toFixed(0)),
                        smoke: Number(sensorData.smoke.toFixed(1)),
                        timestamp: new Date().toISOString()
                    },
                    event_time: this.generateEventTime()
                }]
            };

            this.messageCount++;
            this.lastProcessTime = new Date();

            return {
                success: true,
                data: huaweiMessage,
                originalData: { nodeId, sensorData }
            };
        } catch (error) {
            this.errorCount++;
            console.error('❌ 华为云IoT格式转换失败:', error.message);
            
            return {
                success: false,
                error: error.message,
                originalData: { nodeId, sensorData }
            };
        }
    }

    // 从华为云IoT格式解析数据
    fromHuaweiIoTFormat(huaweiMessage) {
        try {
            if (!huaweiMessage || typeof huaweiMessage !== 'object') {
                throw new Error('Invalid Huawei IoT message format');
            }

            if (!huaweiMessage.services || !Array.isArray(huaweiMessage.services)) {
                throw new Error('Missing services array in Huawei IoT message');
            }

            const results = [];

            huaweiMessage.services.forEach((service, index) => {
                try {
                    if (service.service_id === 'SensorData' && service.properties) {
                        const parsed = this.parseSensorDataService(service);
                        if (parsed.success) {
                            results.push(parsed);
                        }
                    }
                } catch (serviceError) {
                    console.error(`❌ 服务${index}解析失败:`, serviceError.message);
                }
            });

            this.messageCount++;
            this.lastProcessTime = new Date();

            return {
                success: true,
                data: results,
                originalMessage: huaweiMessage
            };
        } catch (error) {
            this.errorCount++;
            console.error('❌ 华为云IoT格式解析失败:', error.message);
            
            return {
                success: false,
                error: error.message,
                originalMessage: huaweiMessage
            };
        }
    }

    // 解析传感器数据服务
    parseSensorDataService(service) {
        const properties = service.properties;
        
        // 提取节点ID
        const nodeId = properties.nodeId || 1; // 默认节点1
        
        // 提取传感器数据
        const sensorData = {
            temperature: this.parseNumber(properties.temperature, 0),
            humidity: this.parseNumber(properties.humidity, 0),
            light: this.parseNumber(properties.light, 0),
            smoke: this.parseNumber(properties.smoke, 0)
        };

        // 验证数据
        const nodeValidation = DataValidator.validateNodeId(nodeId);
        if (!nodeValidation.isValid) {
            throw new Error(`Invalid node ID: ${nodeId}`);
        }

        const dataValidation = DataValidator.validateSensorData(sensorData);
        if (!dataValidation.isValid) {
            throw new Error(`Invalid sensor data: ${dataValidation.errors.join(', ')}`);
        }

        return {
            success: true,
            nodeId: nodeValidation.value,
            sensorData: DataValidator.sanitizeData(sensorData),
            timestamp: properties.timestamp || service.event_time || new Date().toISOString()
        };
    }

    // 解析数字值
    parseNumber(value, defaultValue = 0) {
        if (typeof value === 'number' && !isNaN(value)) {
            return value;
        }
        
        if (typeof value === 'string') {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? defaultValue : parsed;
        }
        
        return defaultValue;
    }

    // 生成华为云IoT事件时间格式
    generateEventTime() {
        // 华为云IoT要求的时间格式: YYYYMMDDTHHMMSSZ
        return new Date().toISOString()
            .replace(/[-:]/g, '')
            .replace(/\.\d{3}/, '') + 'Z';
    }

    // 批量转换数据
    batchToHuaweiIoTFormat(dataArray) {
        const results = [];
        const errors = [];

        dataArray.forEach((item, index) => {
            try {
                const result = this.toHuaweiIoTFormat(item.nodeId, item.sensorData);
                if (result.success) {
                    results.push(result.data);
                } else {
                    errors.push({ index, error: result.error });
                }
            } catch (error) {
                errors.push({ index, error: error.message });
            }
        });

        return {
            success: errors.length === 0,
            results,
            errors,
            totalProcessed: dataArray.length,
            successCount: results.length,
            errorCount: errors.length
        };
    }

    // 批量解析数据
    batchFromHuaweiIoTFormat(messageArray) {
        const results = [];
        const errors = [];

        messageArray.forEach((message, index) => {
            try {
                const result = this.fromHuaweiIoTFormat(message);
                if (result.success) {
                    results.push(...result.data);
                } else {
                    errors.push({ index, error: result.error });
                }
            } catch (error) {
                errors.push({ index, error: error.message });
            }
        });

        return {
            success: errors.length === 0,
            results,
            errors,
            totalProcessed: messageArray.length,
            successCount: results.length,
            errorCount: errors.length
        };
    }

    // 验证华为云IoT消息格式
    validateHuaweiIoTMessage(message) {
        const errors = [];

        if (!message || typeof message !== 'object') {
            errors.push('Message must be an object');
            return { isValid: false, errors };
        }

        if (!message.services || !Array.isArray(message.services)) {
            errors.push('Message must have services array');
        } else if (message.services.length === 0) {
            errors.push('Services array cannot be empty');
        } else {
            message.services.forEach((service, index) => {
                if (!service.service_id) {
                    errors.push(`Service ${index} missing service_id`);
                }
                if (!service.properties || typeof service.properties !== 'object') {
                    errors.push(`Service ${index} missing or invalid properties`);
                }
            });
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 获取适配器统计信息
    getStatistics() {
        return {
            messageCount: this.messageCount,
            errorCount: this.errorCount,
            successRate: this.messageCount > 0 ? 
                ((this.messageCount - this.errorCount) / this.messageCount * 100).toFixed(2) + '%' : '0%',
            lastProcessTime: this.lastProcessTime,
            uptime: this.lastProcessTime ? Date.now() - this.lastProcessTime.getTime() : 0
        };
    }

    // 重置统计信息
    resetStatistics() {
        this.messageCount = 0;
        this.errorCount = 0;
        this.lastProcessTime = null;
        console.log('📊 华为云IoT适配器统计信息已重置');
    }

    // 创建测试数据
    createTestData(nodeId = 1) {
        const testSensorData = {
            temperature: 25.0 + Math.random() * 10,
            humidity: 60.0 + Math.random() * 20,
            light: 500 + Math.random() * 300,
            smoke: 20.0 + Math.random() * 30
        };

        return this.toHuaweiIoTFormat(nodeId, testSensorData);
    }
}

module.exports = HuaweiIoTAdapter;
