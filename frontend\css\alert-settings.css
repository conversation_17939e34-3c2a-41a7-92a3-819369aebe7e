/* 警告设置页面样式 */

/* 设置区域 */
.settings-section {
    margin-bottom: var(--spacing-xxl);
}

.settings-card {
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
}

/* 设置项 */
.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--glass-border);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.setting-desc {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* 切换开关 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-secondary);
    border-radius: 24px;
    transition: all var(--transition-normal);
    border: 1px solid var(--glass-border);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background: white;
    border-radius: 50%;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.toggle-switch input:checked + .toggle-slider {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-switch:hover .toggle-slider {
    box-shadow: var(--shadow-md);
}

/* 阈值设置网格 */
.threshold-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.threshold-card {
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.threshold-card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.threshold-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
}

.sensor-icon {
    font-size: var(--font-size-xl);
}

.sensor-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
}

.sensor-unit {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--background-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.threshold-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.threshold-input {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.threshold-input label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.threshold-input input {
    padding: var(--spacing-sm);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
}

.threshold-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.threshold-preview {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
}

.preview-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.preview-text span {
    font-weight: 600;
    color: var(--text-primary);
}

/* 警告级别设置 */
.level-settings {
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
}

.level-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--glass-border);
}

.level-item:last-child {
    border-bottom: none;
}

.level-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.level-icon {
    font-size: var(--font-size-xl);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.level-icon.error {
    background: var(--error-color-light);
}

.level-icon.warning {
    background: var(--warning-color-light);
}

.level-icon.info {
    background: var(--info-color-light);
}

.level-details h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.level-details p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.test-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.test-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* 操作按钮区域 */
.actions-section {
    margin-top: var(--spacing-xxl);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.action-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: center;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn.secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
}

.action-btn.secondary:hover {
    background: var(--glass-background);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .threshold-grid {
        grid-template-columns: 1fr;
    }
    
    .threshold-controls {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .level-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
}
