// 警告管理器 - 复用Qt项目的警告逻辑
class AlertManager {
    constructor() {
        this.alerts = []; // 存储警告信息
        this.alertsContainer = null;
        this.maxAlerts = CONFIG.alerts.maxHistory; // 最大警告历史数量
        this.autoHideDelay = CONFIG.alerts.autoHideDelay; // 自动隐藏延迟

        // 警告配置 - 复用Qt项目设置
        this.alertConfig = {
            temperature: { min: 0, max: 50, level: 'error' },
            humidity: { min: 10, max: 500, level: 'error' },
            light: { min: 10, max: 800, level: 'warning' },
            smoke: { min: 0, max: 600, level: 'error' }
        };

        // 通知设置
        this.notificationSettings = {
            browserNotifications: true,
            soundAlerts: true,
            popupAlerts: true,
            emailAlerts: false
        };

        // 通知权限状态
        this.notificationPermission = 'default';

        // 声音对象
        this.alertSounds = {
            error: null,
            warning: null,
            info: null
        };

        // 警告队列
        this.alertQueue = [];
        this.isProcessingQueue = false;

        // 初始化
        this.init();
    }

    // 初始化警告管理器
    async init() {
        this.alertsContainer = Utils.dom.$('#alertsContainer');

        // 如果容器不存在，显示空状态
        if (this.alertsContainer) {
            this.showEmptyState();
        }

        // 初始化浏览器通知
        await this.initBrowserNotifications();

        // 初始化声音提示
        this.initSoundAlerts();

        // 加载通知设置
        this.loadNotificationSettings();

        // 开始处理警告队列
        this.startQueueProcessor();

        console.log('⚠️ 警告管理器初始化完成');
    }

    // 添加警告
    addAlert(alert) {
        // 创建警告对象
        const alertObj = {
            id: Utils.generateId(),
            nodeId: alert.nodeId,
            type: alert.type,
            level: alert.level || 'info',
            message: alert.message,
            value: alert.value,
            threshold: alert.threshold,
            timestamp: new Date(),
            isRead: false,
            isActive: true
        };

        // 添加到警告队列
        this.alertQueue.push(alertObj);

        console.log(`🚨 警告已加入队列 [节点${alertObj.nodeId}]:`, alertObj.message);

        return alertObj.id;
    }

    // 检查传感器数据并生成警告
    checkSensorData(nodeId, sensorData) {
        const alerts = [];

        // 检查每个传感器类型
        Object.keys(this.alertConfig).forEach(sensorType => {
            const config = this.alertConfig[sensorType];
            const value = sensorData[sensorType];

            if (value !== undefined) {
                // 检查是否超出范围
                if (value < config.min || value > config.max) {
                    const sensorConfig = CONFIG.utils.getSensorConfig(sensorType);
                    const unit = sensorConfig ? sensorConfig.unit : '';
                    
                    alerts.push({
                        nodeId,
                        type: sensorType,
                        level: config.level,
                        message: `${sensorConfig.name}超出正常范围: ${value}${unit} (正常范围: ${config.min}-${config.max}${unit})`,
                        value,
                        threshold: { min: config.min, max: config.max }
                    });
                }
            }
        });

        // 添加所有检测到的警告
        alerts.forEach(alert => {
            this.addAlert(alert);
        });

        return alerts;
    }

    // 渲染警告项
    renderAlert(alert) {
        if (!this.alertsContainer) return;

        const alertElement = Utils.dom.create('div', {
            className: `alert-item alert-new ${alert.level}`,
            id: `alert-${alert.id}`
        });

        // 获取警告图标
        const alertIcon = this.getAlertIcon(alert.level);
        
        alertElement.innerHTML = `
            <div class="alert-icon ${alert.level}">
                ${alertIcon}
            </div>
            <div class="alert-content">
                <div class="alert-title">节点${alert.nodeId} - ${this.getSensorDisplayName(alert.type)}</div>
                <div class="alert-message">${alert.message}</div>
                <div class="alert-time">${Utils.formatRelativeTime(alert.timestamp)}</div>
            </div>
            <div class="alert-actions">
                <button class="alert-dismiss" onclick="alertManager.dismissAlert('${alert.id}')">
                    ✕
                </button>
            </div>
        `;

        // 添加点击事件
        alertElement.addEventListener('click', () => {
            this.markAsRead(alert.id);
        });

        // 插入到容器顶部
        if (this.alertsContainer.firstChild) {
            this.alertsContainer.insertBefore(alertElement, this.alertsContainer.firstChild);
        } else {
            this.alertsContainer.appendChild(alertElement);
        }

        // 添加动画
        setTimeout(() => {
            Utils.dom.removeClass(alertElement, 'alert-new');
        }, 100);

        // 自动隐藏（如果配置了）
        if (this.autoHideDelay > 0 && alert.level !== 'error') {
            setTimeout(() => {
                this.dismissAlert(alert.id);
            }, this.autoHideDelay);
        }
    }

    // 获取警告图标
    getAlertIcon(level) {
        const icons = {
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            success: '✅'
        };
        return icons[level] || icons.info;
    }

    // 获取传感器显示名称
    getSensorDisplayName(sensorType) {
        const config = CONFIG.utils.getSensorConfig(sensorType);
        return config ? config.name : sensorType;
    }

    // 标记警告为已读
    markAsRead(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert && !alert.isRead) {
            alert.isRead = true;
            
            const alertElement = Utils.dom.$(`#alert-${alertId}`);
            if (alertElement) {
                Utils.dom.addClass(alertElement, 'read');
            }
            
            console.log(`👁️ 警告${alertId}已标记为已读`);
        }
    }

    // 关闭警告
    dismissAlert(alertId) {
        const alertIndex = this.alerts.findIndex(a => a.id === alertId);
        if (alertIndex > -1) {
            this.alerts[alertIndex].isActive = false;
            
            const alertElement = Utils.dom.$(`#alert-${alertId}`);
            if (alertElement) {
                Utils.animation.fadeOut(alertElement, 300);
                setTimeout(() => {
                    if (alertElement.parentNode) {
                        alertElement.parentNode.removeChild(alertElement);
                    }
                }, 300);
            }
            
            // 从数组中移除
            this.alerts.splice(alertIndex, 1);
            
            // 更新容器状态
            this.updateAlertsContainer();
            
            console.log(`🗑️ 警告${alertId}已关闭`);
        }
    }

    // 更新警告容器状态
    updateAlertsContainer() {
        if (!this.alertsContainer) return;

        const activeAlerts = this.getActiveAlerts();
        
        if (activeAlerts.length === 0) {
            this.showEmptyState();
        } else {
            this.hideEmptyState();
        }
    }

    // 显示空状态
    showEmptyState() {
        if (!this.alertsContainer) return;

        // 清空容器
        this.alertsContainer.innerHTML = '';

        const emptyState = Utils.dom.create('div', {
            className: 'empty-state'
        });

        emptyState.innerHTML = `
            <div class="empty-state-icon">🎉</div>
            <div class="empty-state-text">暂无警告</div>
            <div class="empty-state-subtext">系统运行正常</div>
        `;

        this.alertsContainer.appendChild(emptyState);
    }

    // 隐藏空状态
    hideEmptyState() {
        if (!this.alertsContainer) return;

        const emptyState = this.alertsContainer.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    }

    // 触发警告事件
    triggerAlertEvent(alert) {
        // 可以在这里添加声音提示、桌面通知等
        if (alert.level === 'error') {
            // 错误级别的警告可以播放声音
            this.playAlertSound();
        }

        // 发送自定义事件
        const event = new CustomEvent('alertTriggered', {
            detail: alert
        });
        document.dispatchEvent(event);
    }

    // 播放警告声音
    playAlertSound() {
        // 这里可以播放警告声音
        // 由于浏览器限制，需要用户交互后才能播放音频
        console.log('🔊 警告声音触发');
    }

    // 获取活跃警告
    getActiveAlerts() {
        return this.alerts.filter(alert => alert.isActive);
    }

    // 获取未读警告
    getUnreadAlerts() {
        return this.alerts.filter(alert => alert.isActive && !alert.isRead);
    }

    // 获取特定级别的警告
    getAlertsByLevel(level) {
        return this.alerts.filter(alert => alert.isActive && alert.level === level);
    }

    // 获取特定节点的警告
    getAlertsByNode(nodeId) {
        return this.alerts.filter(alert => alert.isActive && alert.nodeId === nodeId);
    }

    // 清除所有警告
    clearAllAlerts() {
        this.alerts = [];
        
        if (this.alertsContainer) {
            this.alertsContainer.innerHTML = '';
            this.showEmptyState();
        }
        
        console.log('🧹 所有警告已清除');
    }

    // 标记所有警告为已读
    markAllAsRead() {
        this.alerts.forEach(alert => {
            if (!alert.isRead) {
                alert.isRead = true;
                
                const alertElement = Utils.dom.$(`#alert-${alert.id}`);
                if (alertElement) {
                    Utils.dom.addClass(alertElement, 'read');
                }
            }
        });
        
        console.log('👁️ 所有警告已标记为已读');
    }

    // 获取警告统计
    getStatistics() {
        const activeAlerts = this.getActiveAlerts();
        
        return {
            total: activeAlerts.length,
            unread: this.getUnreadAlerts().length,
            byLevel: {
                error: this.getAlertsByLevel('error').length,
                warning: this.getAlertsByLevel('warning').length,
                info: this.getAlertsByLevel('info').length
            },
            byNode: {
                1: this.getAlertsByNode(1).length,
                2: this.getAlertsByNode(2).length,
                3: this.getAlertsByNode(3).length
            }
        };
    }

    // 初始化浏览器通知
    async initBrowserNotifications() {
        if ('Notification' in window) {
            this.notificationPermission = Notification.permission;

            if (this.notificationPermission === 'default') {
                try {
                    this.notificationPermission = await Notification.requestPermission();
                    console.log('🔔 浏览器通知权限:', this.notificationPermission);
                } catch (error) {
                    console.warn('⚠️ 请求通知权限失败:', error);
                }
            }
        } else {
            console.warn('⚠️ 浏览器不支持通知API');
        }
    }

    // 初始化声音提示
    initSoundAlerts() {
        try {
            // 创建声音对象（使用Web Audio API生成简单的提示音）
            this.alertSounds.error = this.createAlertSound(800, 0.3, 'square'); // 高频方波
            this.alertSounds.warning = this.createAlertSound(600, 0.2, 'sine'); // 中频正弦波
            this.alertSounds.info = this.createAlertSound(400, 0.1, 'triangle'); // 低频三角波

            console.log('🔊 声音提示初始化完成');
        } catch (error) {
            console.warn('⚠️ 声音提示初始化失败:', error);
        }
    }

    // 创建警告声音
    createAlertSound(frequency, volume, waveType) {
        return {
            frequency,
            volume,
            waveType,
            play: () => {
                if (!this.notificationSettings.soundAlerts) return;

                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                    oscillator.type = waveType;

                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.1);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } catch (error) {
                    console.warn('⚠️ 播放声音失败:', error);
                }
            }
        };
    }

    // 显示浏览器通知
    showBrowserNotification(alert) {
        if (!this.notificationSettings.browserNotifications ||
            this.notificationPermission !== 'granted') {
            return;
        }

        try {
            const notification = new Notification(`节点${alert.nodeId}警告`, {
                body: alert.message,
                icon: this.getAlertIcon(alert.level),
                tag: `alert-${alert.id}`,
                requireInteraction: alert.level === 'error',
                silent: !this.notificationSettings.soundAlerts
            });

            // 点击通知时的处理
            notification.onclick = () => {
                window.focus();
                this.highlightAlert(alert.id);
                notification.close();
            };

            // 自动关闭通知
            setTimeout(() => {
                notification.close();
            }, this.autoHideDelay);

            console.log(`🔔 浏览器通知已显示: ${alert.message}`);
        } catch (error) {
            console.warn('⚠️ 显示浏览器通知失败:', error);
        }
    }

    // 高亮显示警告
    highlightAlert(alertId) {
        const alertElement = Utils.dom.$(`#alert-${alertId}`);
        if (alertElement) {
            alertElement.scrollIntoView({ behavior: 'smooth' });
            Utils.dom.addClass(alertElement, 'highlight');

            setTimeout(() => {
                Utils.dom.removeClass(alertElement, 'highlight');
            }, 2000);
        }
    }

    // 开始队列处理器
    startQueueProcessor() {
        setInterval(() => {
            this.processAlertQueue();
        }, 100); // 每100ms处理一次队列
    }

    // 处理警告队列
    processAlertQueue() {
        if (this.isProcessingQueue || this.alertQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;
        const alert = this.alertQueue.shift();

        // 添加到警告列表
        this.alerts.unshift(alert);

        // 限制警告数量
        if (this.alerts.length > this.maxAlerts) {
            this.alerts = this.alerts.slice(0, this.maxAlerts);
        }

        // 更新UI
        this.renderAlert(alert);
        this.updateAlertsContainer();

        // 显示通知
        if (this.notificationSettings.browserNotifications) {
            this.showBrowserNotification(alert);
        }

        // 播放声音
        if (this.notificationSettings.soundAlerts && this.alertSounds[alert.level]) {
            this.alertSounds[alert.level].play();
        }

        // 触发警告事件
        this.triggerAlertEvent(alert);

        this.isProcessingQueue = false;
        console.log(`🚨 警告已处理 [节点${alert.nodeId}]:`, alert.message);
    }

    // 加载通知设置
    loadNotificationSettings() {
        const savedSettings = Utils.storage.get('notificationSettings');
        if (savedSettings) {
            this.notificationSettings = { ...this.notificationSettings, ...savedSettings };
        }
        console.log('📱 通知设置已加载:', this.notificationSettings);
    }

    // 保存通知设置
    saveNotificationSettings() {
        Utils.storage.set('notificationSettings', this.notificationSettings);
        console.log('💾 通知设置已保存');
    }

    // 更新通知设置
    updateNotificationSettings(newSettings) {
        this.notificationSettings = { ...this.notificationSettings, ...newSettings };
        this.saveNotificationSettings();
        console.log('⚙️ 通知设置已更新:', this.notificationSettings);
    }

    // 导出警告报告
    exportAlertReport(format = 'json') {
        const report = {
            exportTime: new Date().toISOString(),
            totalAlerts: this.alerts.length,
            alertsByLevel: this.getAlertsByLevel(),
            alertsByNode: this.getAlertsByNode(),
            recentAlerts: this.alerts.slice(0, 50), // 最近50条警告
            statistics: this.getStatistics()
        };

        const filename = `alert_report_${this.getTimestamp()}.${format}`;

        if (format === 'json') {
            const jsonString = JSON.stringify(report, null, 2);
            this.downloadFile(jsonString, filename, 'application/json');
        } else if (format === 'csv') {
            const csvContent = this.convertAlertsToCSV(this.alerts);
            this.downloadFile(csvContent, filename, 'text/csv');
        }

        console.log(`📄 警告报告已导出: ${filename}`);
    }

    // 转换警告为CSV格式
    convertAlertsToCSV(alerts) {
        let csv = '时间,节点ID,类型,级别,消息,数值,阈值\n';

        alerts.forEach(alert => {
            const row = [
                alert.timestamp.toISOString(),
                alert.nodeId,
                alert.type,
                alert.level,
                `"${alert.message}"`,
                alert.value || '',
                alert.threshold ? `${alert.threshold.min}-${alert.threshold.max}` : ''
            ].join(',');
            csv += row + '\n';
        });

        return csv;
    }

    // 下载文件
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 100);
    }

    // 获取时间戳
    getTimestamp() {
        return new Date().toISOString()
            .replace(/[:.]/g, '-')
            .replace('T', '_')
            .split('.')[0];
    }

    // 更新警告配置
    updateAlertConfig(newConfig) {
        this.alertConfig = { ...this.alertConfig, ...newConfig };
        console.log('⚙️ 警告配置已更新:', this.alertConfig);
    }
}

// 导出警告管理器
window.AlertManager = AlertManager;
