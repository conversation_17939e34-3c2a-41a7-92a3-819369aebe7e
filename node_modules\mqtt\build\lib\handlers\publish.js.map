{"version": 3, "file": "publish.js", "sourceRoot": "", "sources": ["../../../src/lib/handlers/publish.ts"], "names": [], "mappings": ";;AAGA,MAAM,gBAAgB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AA0BnE,MAAM,aAAa,GAAkB,CAAC,MAAM,EAAE,MAAsB,EAAE,IAAI,EAAE,EAAE;IAC7E,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAA;IAC9C,IAAI,GAAG,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAA;IACvD,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAA;IACnC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAA;IAC9B,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAA;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA;IAC5B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA;IAC1B,IAAI,MAAM,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;QAC1C,IAAI,KAAa,CAAA;QACjB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACvB,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,CAAA;QACrC,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;oBAClC,MAAM,QAAQ,GACb,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;oBAChD,IAAI,QAAQ,EAAE,CAAC;wBACd,KAAK,GAAG,QAAQ,CAAA;wBAChB,MAAM,CAAC,GAAG,CACT,qEAAqE,EACrE,KAAK,EACL,KAAK,CACL,CAAA;oBACF,CAAC;yBAAM,CAAC;wBACP,MAAM,CAAC,GAAG,CACT,sDAAsD,EACtD,KAAK,CACL,CAAA;wBACD,MAAM,CAAC,IAAI,CACV,OAAO,EACP,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAC9C,CAAA;wBACD,OAAM;oBACP,CAAC;gBACF,CAAC;qBAAM,CAAC;oBACP,MAAM,CAAC,GAAG,CACT,sDAAsD,EACtD,KAAK,CACL,CAAA;oBACD,MAAM,CAAC,IAAI,CACV,OAAO,EACP,IAAI,KAAK,CAAC,sCAAsC,CAAC,CACjD,CAAA;oBACD,OAAM;gBACP,CAAC;YACF,CAAC;iBAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC;gBACvD,MAAM,CAAC,GAAG,CACT,mDAAmD,EACnD,KAAK,EACL,KAAK,CACL,CAAA;YACF,CAAC;iBAAM,CAAC;gBACP,MAAM,CAAC,GAAG,CACT,sDAAsD,EACtD,KAAK,CACL,CAAA;gBACD,MAAM,CAAC,IAAI,CACV,OAAO,EACP,IAAI,KAAK,CAAC,sCAAsC,CAAC,CACjD,CAAA;gBACD,OAAM;YACP,CAAC;QACF,CAAC;IACF,CAAC;IACD,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAA;IACxC,QAAQ,GAAG,EAAE,CAAC;QACb,KAAK,CAAC,CAAC,CAAC,CAAC;YACR,OAAO,CAAC,gBAAgB,CACvB,KAAK,EACL,OAAiB,EACjB,MAAM,EACN,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACf,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC/B,IAAI,GAAG,KAAK,CAAA;oBACZ,KAAK,GAAG,IAAI,CAAA;gBACb,CAAC;gBACD,IAAI,KAAK,EAAE,CAAC;oBACX,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAc,CAAC,CAAA;gBAC5C,CAAC;gBACD,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC3C,OAAO,MAAM,CAAC,IAAI,CACjB,OAAO,EACP,IAAI,KAAK,CAAC,8BAA8B,CAAC,CACzC,CAAA;gBACF,CAAC;gBACD,IAAI,IAAI,EAAE,CAAC;oBACV,MAAM,CAAC,aAAa,CAAC,CACpB,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,EAC9C,IAAI,CACJ,CAAA;gBACF,CAAC;qBAAM,CAAC;oBACP,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE;wBACrC,MAAM,CAAC,aAAa,CAAC,CACpB,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,EAC5B,IAAI,CACJ,CAAA;oBACF,CAAC,CAAC,CAAA;gBACH,CAAC;YACF,CAAC,CACD,CAAA;YACD,MAAK;QACN,CAAC;QACD,KAAK,CAAC,CAAC,CAAC,CAAC;YAER,OAAO,CAAC,gBAAgB,CACvB,KAAK,EACL,OAAiB,EACjB,MAAM,EACN,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACf,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC/B,IAAI,GAAG,KAAK,CAAA;oBACZ,KAAK,GAAG,IAAI,CAAA;gBACb,CAAC;gBACD,IAAI,KAAK,EAAE,CAAC;oBACX,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAc,CAAC,CAAA;gBAC5C,CAAC;gBACD,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC3C,OAAO,MAAM,CAAC,IAAI,CACjB,OAAO,EACP,IAAI,KAAK,CAAC,8BAA8B,CAAC,CACzC,CAAA;gBACF,CAAC;gBACD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACX,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,OAAiB,EAAE,MAAM,CAAC,CAAA;gBACzD,CAAC;gBACD,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;oBACpC,IAAI,GAAG,EAAE,CAAC;wBACT,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,CAAC;oBACD,MAAM,CAAC,aAAa,CAAC,CACpB,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,EAC9C,IAAI,CACJ,CAAA;gBACF,CAAC,CAAC,CAAA;YACH,CAAC,CACD,CAAA;YACD,MAAK;QACN,CAAC;QACD,KAAK,CAAC;YAEL,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,OAAiB,EAAE,MAAM,CAAC,CAAA;YACxD,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YAClC,MAAK;QACN;YAEC,MAAM,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;YAExD,MAAK;IACP,CAAC;AACF,CAAC,CAAA;AAED,kBAAe,aAAa,CAAA"}