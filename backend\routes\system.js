// 系统相关API路由
const express = require('express');
const router = express.Router();
const dataManager = require('../services/dataManager');
const ResponseHandler = require('../middleware/responseHandler');
const { asyncHandler } = require('../middleware/errorHandler');

// GET /api/system/status - 获取系统状态
router.get('/status', asyncHandler(async (req, res) => {
    const systemStatus = dataManager.getSystemStatus();
    
    // 添加额外的系统信息
    const extendedStatus = {
        ...systemStatus,
        server: {
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
            memory: {
                used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
            }
        },
        api: {
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development'
        }
    };
    
    return ResponseHandler.success(res, extendedStatus, '成功获取系统状态');
}));

// GET /api/system/health - 健康检查
router.get('/health', asyncHandler(async (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        checks: {
            dataManager: 'ok',
            memory: process.memoryUsage().heapUsed < 100 * 1024 * 1024 ? 'ok' : 'warning', // 100MB阈值
            nodes: dataManager.getAllNodes().length === 3 ? 'ok' : 'error'
        }
    };
    
    // 检查是否有任何检查失败
    const hasErrors = Object.values(health.checks).includes('error');
    const hasWarnings = Object.values(health.checks).includes('warning');
    
    if (hasErrors) {
        health.status = 'unhealthy';
        return ResponseHandler.error(res, '系统健康检查失败', 503, health);
    } else if (hasWarnings) {
        health.status = 'degraded';
    }
    
    return ResponseHandler.success(res, health, '系统健康检查通过');
}));

// GET /api/system/stats - 获取系统统计信息
router.get('/stats', asyncHandler(async (req, res) => {
    const hours = parseInt(req.query.hours) || 24;
    
    // 验证时间范围
    if (hours < 1 || hours > 168) {
        return ResponseHandler.validationError(res, '时间范围必须在1-168小时之间');
    }
    
    const stats = dataManager.getDataStatistics(hours);
    const systemStatus = dataManager.getSystemStatus();
    
    const extendedStats = {
        ...stats,
        system: {
            uptime: systemStatus.uptime,
            totalDataPoints: systemStatus.totalDataPoints,
            totalAlerts: systemStatus.totalAlerts,
            lastUpdate: systemStatus.lastUpdate
        },
        performance: {
            memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            cpuUsage: process.cpuUsage()
        }
    };
    
    return ResponseHandler.success(res, extendedStats, '成功获取系统统计信息');
}));

// POST /api/system/reset - 重置系统数据
router.post('/reset', asyncHandler(async (req, res) => {
    const { confirm } = req.body;
    
    if (!confirm) {
        return ResponseHandler.validationError(res, '需要确认重置操作');
    }
    
    const result = dataManager.resetAll();
    
    return ResponseHandler.success(res, result, '系统数据重置成功');
}));

// GET /api/system/info - 获取系统信息
router.get('/info', asyncHandler(async (req, res) => {
    const info = {
        name: 'Loong Terminal Web System',
        version: '1.0.0',
        description: '龙芯智能终端管理系统Web版',
        author: 'Loong Terminal Team',
        license: 'MIT',
        repository: 'https://github.com/loong-terminal/web-system',
        features: [
            '实时传感器数据监控',
            '三节点状态管理',
            '智能警告系统',
            '华为云IoT集成',
            '数据可视化',
            'RESTful API',
            'WebSocket实时通信'
        ],
        sensors: {
            types: ['temperature', 'humidity', 'light', 'smoke'],
            ranges: {
                temperature: '0-50℃',
                humidity: '10-500%RH',
                light: '10-800lm',
                smoke: '0-600mg'
            }
        },
        nodes: {
            count: 3,
            names: ['节点一', '节点二', '节点三']
        }
    };
    
    return ResponseHandler.success(res, info, '成功获取系统信息');
}));

// GET /api/system/config - 获取系统配置
router.get('/config', asyncHandler(async (req, res) => {
    const config = {
        updateInterval: 50, // 50ms更新间隔
        offlineTimeout: 5000, // 5秒离线超时
        maxHistoryPoints: 100, // 最大历史数据点
        maxAlertHistory: 50, // 最大警告历史
        chartRanges: {
            temperature: { min: 0, max: 39 },
            humidity: { min: 0, max: 99 },
            light: { min: 0, max: 1000 },
            smoke: { min: 0, max: 2000 }
        },
        api: {
            version: '1.0.0',
            baseUrl: '/api',
            rateLimit: {
                windowMs: 15 * 60 * 1000, // 15分钟
                max: 1000 // 最大请求数
            }
        }
    };
    
    return ResponseHandler.success(res, config, '成功获取系统配置');
}));

// GET /api/system/logs - 获取系统日志（简化版）
router.get('/logs', asyncHandler(async (req, res) => {
    const level = req.query.level || 'info';
    const limit = parseInt(req.query.limit) || 50;

    if (limit < 1 || limit > 1000) {
        return ResponseHandler.validationError(res, '日志数量限制必须在1-1000之间');
    }

    // 模拟日志数据（实际应用中应该从日志文件或数据库读取）
    const logs = [
        {
            timestamp: new Date().toISOString(),
            level: 'info',
            message: '系统正常运行',
            module: 'system'
        },
        {
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'info',
            message: '数据管理器初始化完成',
            module: 'dataManager'
        }
    ];

    const filteredLogs = logs.filter(log =>
        level === 'all' || log.level === level
    ).slice(0, limit);

    return ResponseHandler.success(res, filteredLogs, '成功获取系统日志');
}));

// GET /api/system/websocket - 获取WebSocket连接状态
router.get('/websocket', asyncHandler(async (req, res) => {
    // 这里需要从WebSocket服务获取状态
    // 暂时返回模拟数据
    const websocketStatus = {
        enabled: true,
        totalConnections: 0,
        activeConnections: 0,
        updateInterval: 50,
        lastUpdate: new Date().toISOString(),
        rooms: {
            'node-1': 0,
            'node-2': 0,
            'node-3': 0,
            'all-nodes': 0
        }
    };

    return ResponseHandler.success(res, websocketStatus, '成功获取WebSocket状态');
}));

// GET /api/system/mqtt - 获取MQTT连接状态
router.get('/mqtt', asyncHandler(async (req, res) => {
    // 这里需要从MQTT服务获取状态
    // 暂时返回模拟数据
    const mqttStatus = {
        enabled: true,
        isConnected: false,
        deviceId: '685a734ad582f2001834985f_loong_1',
        hostname: '5930c00e73.st1.iotda-device.cn-north-4.myhuaweicloud.com',
        port: 8883,
        lastConnectTime: null,
        messagesReceived: 0,
        messagesSent: 0,
        errors: 0,
        reconnectAttempts: 0
    };

    return ResponseHandler.success(res, mqttStatus, '成功获取MQTT状态');
}));

// POST /api/system/mqtt/start - 启动MQTT服务
router.post('/mqtt/start', asyncHandler(async (req, res) => {
    // 这里需要启动MQTT服务
    const result = {
        success: true,
        message: 'MQTT服务启动成功',
        timestamp: new Date().toISOString()
    };

    return ResponseHandler.success(res, result, 'MQTT服务启动成功');
}));

// POST /api/system/mqtt/stop - 停止MQTT服务
router.post('/mqtt/stop', asyncHandler(async (req, res) => {
    // 这里需要停止MQTT服务
    const result = {
        success: true,
        message: 'MQTT服务停止成功',
        timestamp: new Date().toISOString()
    };

    return ResponseHandler.success(res, result, 'MQTT服务停止成功');
}));

module.exports = router;
