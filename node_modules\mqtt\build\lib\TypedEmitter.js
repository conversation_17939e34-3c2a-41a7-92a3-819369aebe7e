"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypedEventEmitter = void 0;
const events_1 = __importDefault(require("events"));
const shared_1 = require("./shared");
class TypedEventEmitter {
}
exports.TypedEventEmitter = TypedEventEmitter;
(0, shared_1.applyMixin)(TypedEventEmitter, events_1.default);
//# sourceMappingURL=TypedEmitter.js.map