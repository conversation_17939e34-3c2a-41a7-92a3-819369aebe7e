// 警告配置模型 - 复用Qt项目警告设置
class AlertConfig {
    constructor(nodeId) {
        this.nodeId = nodeId; // 节点ID
        this.tempRange = { min: 0, max: 50 }; // 温度范围 - 复用Qt设置
        this.humiRange = { min: 10, max: 500 }; // 湿度范围
        this.lightRange = { min: 10, max: 800 }; // 光照范围
        this.smokeMax = 600; // 烟雾最大值
        this.enabled = true; // 是否启用警告
        this.notificationSettings = {
            email: false,
            browser: true,
            sound: false
        };
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    // 创建警告配置
    static create(nodeId, config = {}) {
        if (!nodeId || ![1, 2, 3].includes(Number(nodeId))) {
            throw new Error('Invalid node ID. Must be 1, 2, or 3');
        }

        const alertConfig = new AlertConfig(Number(nodeId));
        
        // 应用自定义配置
        if (config.tempRange) {
            alertConfig.updateTempRange(config.tempRange.min, config.tempRange.max);
        }
        if (config.humiRange) {
            alertConfig.updateHumiRange(config.humiRange.min, config.humiRange.max);
        }
        if (config.lightRange) {
            alertConfig.updateLightRange(config.lightRange.min, config.lightRange.max);
        }
        if (config.smokeMax !== undefined) {
            alertConfig.updateSmokeMax(config.smokeMax);
        }
        if (config.enabled !== undefined) {
            alertConfig.enabled = config.enabled;
        }
        if (config.notificationSettings) {
            alertConfig.updateNotificationSettings(config.notificationSettings);
        }

        return alertConfig;
    }

    // 更新温度范围
    updateTempRange(min, max) {
        if (typeof min !== 'number' || typeof max !== 'number') {
            throw new Error('Temperature range values must be numbers');
        }
        if (min >= max) {
            throw new Error('Temperature min must be less than max');
        }
        
        this.tempRange = { min, max };
        this.updatedAt = new Date();
    }

    // 更新湿度范围
    updateHumiRange(min, max) {
        if (typeof min !== 'number' || typeof max !== 'number') {
            throw new Error('Humidity range values must be numbers');
        }
        if (min >= max) {
            throw new Error('Humidity min must be less than max');
        }
        
        this.humiRange = { min, max };
        this.updatedAt = new Date();
    }

    // 更新光照范围
    updateLightRange(min, max) {
        if (typeof min !== 'number' || typeof max !== 'number') {
            throw new Error('Light range values must be numbers');
        }
        if (min >= max) {
            throw new Error('Light min must be less than max');
        }
        
        this.lightRange = { min, max };
        this.updatedAt = new Date();
    }

    // 更新烟雾最大值
    updateSmokeMax(max) {
        if (typeof max !== 'number' || max < 0) {
            throw new Error('Smoke max must be a positive number');
        }
        
        this.smokeMax = max;
        this.updatedAt = new Date();
    }

    // 更新通知设置
    updateNotificationSettings(settings) {
        this.notificationSettings = {
            ...this.notificationSettings,
            ...settings
        };
        this.updatedAt = new Date();
    }

    // 检查传感器数据是否超出阈值
    checkThresholds(sensorData) {
        if (!this.enabled) return [];

        const alerts = [];
        
        // 检查温度
        if (sensorData.temperature < this.tempRange.min || sensorData.temperature > this.tempRange.max) {
            alerts.push({
                type: 'temperature',
                level: 'error',
                message: `温度超出范围 (${this.tempRange.min}-${this.tempRange.max}℃): ${sensorData.temperature}℃`,
                threshold: this.tempRange,
                value: sensorData.temperature
            });
        }

        // 检查湿度
        if (sensorData.humidity < this.humiRange.min || sensorData.humidity > this.humiRange.max) {
            alerts.push({
                type: 'humidity',
                level: 'error',
                message: `湿度超出范围 (${this.humiRange.min}-${this.humiRange.max}%RH): ${sensorData.humidity}%RH`,
                threshold: this.humiRange,
                value: sensorData.humidity
            });
        }

        // 检查光照
        if (sensorData.light < this.lightRange.min || sensorData.light > this.lightRange.max) {
            alerts.push({
                type: 'light',
                level: 'warning',
                message: `光照超出范围 (${this.lightRange.min}-${this.lightRange.max}lm): ${sensorData.light}lm`,
                threshold: this.lightRange,
                value: sensorData.light
            });
        }

        // 检查烟雾
        if (sensorData.smoke > this.smokeMax) {
            alerts.push({
                type: 'smoke',
                level: 'error',
                message: `烟雾浓度过高 (>${this.smokeMax}mg): ${sensorData.smoke}mg`,
                threshold: { max: this.smokeMax },
                value: sensorData.smoke
            });
        }

        return alerts;
    }

    // 获取默认配置 - 复用Qt项目设置
    static getDefaultConfig() {
        return {
            tempRange: { min: 0, max: 50 },
            humiRange: { min: 10, max: 500 },
            lightRange: { min: 10, max: 800 },
            smokeMax: 600,
            enabled: true,
            notificationSettings: {
                email: false,
                browser: true,
                sound: false
            }
        };
    }

    // 转换为JSON格式
    toJSON() {
        return {
            nodeId: this.nodeId,
            tempRange: this.tempRange,
            humiRange: this.humiRange,
            lightRange: this.lightRange,
            smokeMax: this.smokeMax,
            enabled: this.enabled,
            notificationSettings: this.notificationSettings,
            createdAt: this.createdAt.toISOString(),
            updatedAt: this.updatedAt.toISOString()
        };
    }

    // 验证配置有效性
    validate() {
        const errors = [];

        if (this.tempRange.min >= this.tempRange.max) {
            errors.push('Temperature min must be less than max');
        }
        if (this.humiRange.min >= this.humiRange.max) {
            errors.push('Humidity min must be less than max');
        }
        if (this.lightRange.min >= this.lightRange.max) {
            errors.push('Light min must be less than max');
        }
        if (this.smokeMax < 0) {
            errors.push('Smoke max must be positive');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

module.exports = AlertConfig;
