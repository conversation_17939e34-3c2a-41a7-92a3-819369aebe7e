// Socket连接管理工具
class SocketManager {
    constructor() {
        this.connections = new Map(); // 存储所有连接
        this.rooms = new Map(); // 房间管理
        this.statistics = {
            totalConnections: 0,
            currentConnections: 0,
            peakConnections: 0,
            totalMessages: 0,
            startTime: new Date()
        };
    }

    // 注册新连接
    registerConnection(socket) {
        const connectionInfo = {
            id: socket.id,
            connectedAt: new Date(),
            lastActivity: new Date(),
            messageCount: 0,
            subscribedRooms: new Set(),
            clientInfo: {
                userAgent: socket.handshake.headers['user-agent'] || 'Unknown',
                ip: socket.handshake.address,
                origin: socket.handshake.headers.origin
            }
        };

        this.connections.set(socket.id, connectionInfo);
        this.statistics.totalConnections++;
        this.statistics.currentConnections++;
        
        // 更新峰值连接数
        if (this.statistics.currentConnections > this.statistics.peakConnections) {
            this.statistics.peakConnections = this.statistics.currentConnections;
        }

        console.log(`📱 新连接注册: ${socket.id} (当前: ${this.statistics.currentConnections})`);
        return connectionInfo;
    }

    // 注销连接
    unregisterConnection(socketId) {
        const connection = this.connections.get(socketId);
        if (connection) {
            // 从所有房间中移除
            connection.subscribedRooms.forEach(room => {
                this.leaveRoom(socketId, room);
            });

            this.connections.delete(socketId);
            this.statistics.currentConnections--;
            
            const duration = Date.now() - connection.connectedAt.getTime();
            console.log(`📱 连接注销: ${socketId} (持续: ${Math.round(duration/1000)}s, 消息: ${connection.messageCount})`);
        }
    }

    // 更新连接活动
    updateActivity(socketId) {
        const connection = this.connections.get(socketId);
        if (connection) {
            connection.lastActivity = new Date();
            connection.messageCount++;
            this.statistics.totalMessages++;
        }
    }

    // 加入房间
    joinRoom(socketId, roomName) {
        const connection = this.connections.get(socketId);
        if (!connection) return false;

        // 更新连接信息
        connection.subscribedRooms.add(roomName);

        // 更新房间信息
        if (!this.rooms.has(roomName)) {
            this.rooms.set(roomName, {
                name: roomName,
                members: new Set(),
                createdAt: new Date(),
                messageCount: 0
            });
        }

        const room = this.rooms.get(roomName);
        room.members.add(socketId);

        console.log(`🏠 ${socketId} 加入房间 ${roomName} (房间成员: ${room.members.size})`);
        return true;
    }

    // 离开房间
    leaveRoom(socketId, roomName) {
        const connection = this.connections.get(socketId);
        if (connection) {
            connection.subscribedRooms.delete(roomName);
        }

        const room = this.rooms.get(roomName);
        if (room) {
            room.members.delete(socketId);
            
            // 如果房间为空，删除房间
            if (room.members.size === 0) {
                this.rooms.delete(roomName);
                console.log(`🏠 房间 ${roomName} 已删除（无成员）`);
            } else {
                console.log(`🏠 ${socketId} 离开房间 ${roomName} (剩余成员: ${room.members.size})`);
            }
        }
    }

    // 获取房间成员
    getRoomMembers(roomName) {
        const room = this.rooms.get(roomName);
        return room ? Array.from(room.members) : [];
    }

    // 获取连接信息
    getConnection(socketId) {
        return this.connections.get(socketId);
    }

    // 获取所有活跃连接
    getActiveConnections(inactiveThreshold = 60000) { // 1分钟
        const now = new Date();
        const activeConnections = [];

        for (const [socketId, connection] of this.connections) {
            const inactiveTime = now - connection.lastActivity;
            if (inactiveTime < inactiveThreshold) {
                activeConnections.push({
                    ...connection,
                    inactiveTime: Math.round(inactiveTime / 1000)
                });
            }
        }

        return activeConnections;
    }

    // 获取统计信息
    getStatistics() {
        const now = new Date();
        const uptime = now - this.statistics.startTime;
        
        return {
            ...this.statistics,
            uptime: Math.round(uptime / 1000),
            activeConnections: this.getActiveConnections().length,
            totalRooms: this.rooms.size,
            averageMessagesPerConnection: this.statistics.totalConnections > 0 
                ? Math.round(this.statistics.totalMessages / this.statistics.totalConnections)
                : 0
        };
    }

    // 获取房间统计
    getRoomStatistics() {
        const roomStats = {};
        
        for (const [roomName, room] of this.rooms) {
            roomStats[roomName] = {
                memberCount: room.members.size,
                createdAt: room.createdAt,
                messageCount: room.messageCount,
                members: Array.from(room.members)
            };
        }
        
        return roomStats;
    }

    // 广播到房间
    broadcastToRoom(io, roomName, event, data) {
        const room = this.rooms.get(roomName);
        if (room) {
            room.messageCount++;
            io.to(roomName).emit(event, data);
            return room.members.size;
        }
        return 0;
    }

    // 清理非活跃连接
    cleanupInactiveConnections(inactiveThreshold = 300000) { // 5分钟
        const now = new Date();
        const toRemove = [];

        for (const [socketId, connection] of this.connections) {
            const inactiveTime = now - connection.lastActivity;
            if (inactiveTime > inactiveThreshold) {
                toRemove.push(socketId);
            }
        }

        toRemove.forEach(socketId => {
            console.log(`🧹 清理非活跃连接: ${socketId}`);
            this.unregisterConnection(socketId);
        });

        return toRemove.length;
    }

    // 获取连接详情
    getConnectionDetails() {
        const details = [];
        
        for (const [socketId, connection] of this.connections) {
            const now = new Date();
            const duration = now - connection.connectedAt;
            const inactiveTime = now - connection.lastActivity;
            
            details.push({
                id: socketId,
                connectedAt: connection.connectedAt.toISOString(),
                duration: Math.round(duration / 1000),
                lastActivity: connection.lastActivity.toISOString(),
                inactiveTime: Math.round(inactiveTime / 1000),
                messageCount: connection.messageCount,
                subscribedRooms: Array.from(connection.subscribedRooms),
                clientInfo: connection.clientInfo
            });
        }
        
        return details.sort((a, b) => new Date(b.connectedAt) - new Date(a.connectedAt));
    }

    // 重置统计信息
    resetStatistics() {
        this.statistics = {
            totalConnections: this.statistics.currentConnections,
            currentConnections: this.statistics.currentConnections,
            peakConnections: this.statistics.currentConnections,
            totalMessages: 0,
            startTime: new Date()
        };
        
        console.log('📊 统计信息已重置');
    }

    // 导出统计报告
    exportReport() {
        const report = {
            timestamp: new Date().toISOString(),
            statistics: this.getStatistics(),
            roomStatistics: this.getRoomStatistics(),
            connectionDetails: this.getConnectionDetails(),
            summary: {
                totalConnections: this.statistics.totalConnections,
                currentConnections: this.statistics.currentConnections,
                peakConnections: this.statistics.peakConnections,
                totalMessages: this.statistics.totalMessages,
                totalRooms: this.rooms.size,
                uptime: Math.round((new Date() - this.statistics.startTime) / 1000)
            }
        };
        
        return report;
    }
}

module.exports = SocketManager;
