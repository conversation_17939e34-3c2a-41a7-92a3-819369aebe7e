/* 节点详情页面样式 */

/* 返回按钮 */
.back-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--glass-background);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    backdrop-filter: var(--glass-blur);
}

.back-button:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-2px);
}

.back-icon {
    font-size: var(--font-size-lg);
    transition: transform var(--transition-normal);
}

.back-button:hover .back-icon {
    transform: translateX(-2px);
}

/* 页面标题 */
.page-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* 节点概览卡片 */
.node-overview {
    margin-bottom: var(--spacing-xl);
}

.node-overview-card {
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.node-overview-card:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
}

.node-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.node-name {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.node-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.node-status.online {
    background: var(--success-color-light);
    color: var(--success-color);
}

.node-status.offline {
    background: var(--error-color-light);
    color: var(--error-color);
}

.node-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.action-btn:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn:active {
    transform: translateY(0);
}

/* 节点统计 */
.node-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.stat-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.stat-label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-item:hover .stat-label {
    color: rgba(255, 255, 255, 0.8);
}

.stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.stat-item:hover .stat-value {
    color: white;
}

/* 传感器卡片网格 */
.sensor-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.sensor-card {
    background: var(--glass-background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.sensor-card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    transform: translateY(-4px);
}

.sensor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.sensor-card:hover::before {
    transform: scaleX(1);
}

.sensor-card[data-sensor="temperature"]::before {
    background: #FF6B6B;
}

.sensor-card[data-sensor="humidity"]::before {
    background: #4ECDC4;
}

.sensor-card[data-sensor="light"]::before {
    background: #FFE66D;
}

.sensor-card[data-sensor="smoke"]::before {
    background: #A8E6CF;
}

.sensor-icon {
    font-size: var(--font-size-xxxl);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.sensor-name {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    text-align: center;
}

.sensor-value {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.sensor-card:hover .sensor-value {
    transform: scale(1.05);
}

.sensor-status {
    text-align: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sensor-status.normal {
    background: var(--success-color-light);
    color: var(--success-color);
}

.sensor-status.warning {
    background: var(--warning-color-light);
    color: var(--warning-color);
}

.sensor-status.error {
    background: var(--error-color-light);
    color: var(--error-color);
}

/* 警告过滤器 */
.alert-filters {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm);
    background: var(--glass-background);
    border-radius: var(--radius-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-btn:hover {
    background: var(--primary-color);
    color: white;
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* 警告列表 */
.alert-list {
    max-height: 400px;
    overflow-y: auto;
    background: var(--glass-background);
    border-radius: var(--radius-lg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
}

.alert-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-item:hover {
    background: var(--background-secondary);
}

.alert-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.alert-message {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.alert-time {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .node-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .node-info {
        justify-content: center;
    }
    
    .node-actions {
        justify-content: center;
    }
    
    .sensor-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .alert-filters {
        flex-wrap: wrap;
    }
}
