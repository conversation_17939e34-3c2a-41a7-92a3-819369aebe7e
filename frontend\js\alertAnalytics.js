// 警告分析工具 - 提供警告数据的统计分析功能
class AlertAnalytics {
    constructor() {
        this.alertHistory = [];
        this.analysisCache = new Map();
        this.cacheTimeout = 60000; // 1分钟缓存
        
        console.log('📊 警告分析工具初始化完成');
    }

    // 设置警告历史数据
    setAlertHistory(alerts) {
        this.alertHistory = alerts || [];
        this.clearCache();
        console.log(`📊 警告历史数据已更新: ${this.alertHistory.length}条记录`);
    }

    // 清除缓存
    clearCache() {
        this.analysisCache.clear();
    }

    // 获取缓存或计算结果
    getCachedOrCompute(key, computeFunction) {
        const cached = this.analysisCache.get(key);
        const now = Date.now();
        
        if (cached && (now - cached.timestamp) < this.cacheTimeout) {
            return cached.data;
        }
        
        const result = computeFunction();
        this.analysisCache.set(key, {
            data: result,
            timestamp: now
        });
        
        return result;
    }

    // 基础统计信息
    getBasicStatistics() {
        return this.getCachedOrCompute('basicStats', () => {
            const total = this.alertHistory.length;
            const byLevel = this.groupByLevel();
            const byNode = this.groupByNode();
            const bySensor = this.groupBySensorType();
            
            return {
                total,
                byLevel,
                byNode,
                bySensor,
                averagePerDay: this.getAverageAlertsPerDay(),
                mostActiveNode: this.getMostActiveNode(),
                mostProblematicSensor: this.getMostProblematicSensor()
            };
        });
    }

    // 按级别分组
    groupByLevel() {
        const groups = { error: 0, warning: 0, info: 0 };
        
        this.alertHistory.forEach(alert => {
            if (groups.hasOwnProperty(alert.level)) {
                groups[alert.level]++;
            }
        });
        
        return groups;
    }

    // 按节点分组
    groupByNode() {
        const groups = {};
        
        this.alertHistory.forEach(alert => {
            const nodeId = alert.nodeId;
            if (!groups[nodeId]) {
                groups[nodeId] = 0;
            }
            groups[nodeId]++;
        });
        
        return groups;
    }

    // 按传感器类型分组
    groupBySensorType() {
        const groups = {};
        
        this.alertHistory.forEach(alert => {
            const sensorType = alert.type;
            if (!groups[sensorType]) {
                groups[sensorType] = 0;
            }
            groups[sensorType]++;
        });
        
        return groups;
    }

    // 获取每日平均警告数
    getAverageAlertsPerDay() {
        if (this.alertHistory.length === 0) return 0;
        
        const dates = this.alertHistory.map(alert => 
            new Date(alert.timestamp).toDateString()
        );
        
        const uniqueDates = [...new Set(dates)];
        return Math.round(this.alertHistory.length / uniqueDates.length * 100) / 100;
    }

    // 获取最活跃的节点
    getMostActiveNode() {
        const byNode = this.groupByNode();
        let maxCount = 0;
        let mostActiveNode = null;
        
        Object.keys(byNode).forEach(nodeId => {
            if (byNode[nodeId] > maxCount) {
                maxCount = byNode[nodeId];
                mostActiveNode = nodeId;
            }
        });
        
        return mostActiveNode ? {
            nodeId: mostActiveNode,
            alertCount: maxCount
        } : null;
    }

    // 获取最有问题的传感器
    getMostProblematicSensor() {
        const bySensor = this.groupBySensorType();
        let maxCount = 0;
        let mostProblematicSensor = null;
        
        Object.keys(bySensor).forEach(sensorType => {
            if (bySensor[sensorType] > maxCount) {
                maxCount = bySensor[sensorType];
                mostProblematicSensor = sensorType;
            }
        });
        
        return mostProblematicSensor ? {
            sensorType: mostProblematicSensor,
            alertCount: maxCount
        } : null;
    }

    // 时间趋势分析
    getTimeTrendAnalysis(timeRange = '24h') {
        return this.getCachedOrCompute(`timeTrend_${timeRange}`, () => {
            const now = new Date();
            let startTime;
            
            switch (timeRange) {
                case '1h':
                    startTime = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case '24h':
                    startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case '7d':
                    startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case '30d':
                    startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            }
            
            const filteredAlerts = this.alertHistory.filter(alert => 
                new Date(alert.timestamp) >= startTime
            );
            
            return this.generateTimeSeriesData(filteredAlerts, timeRange);
        });
    }

    // 生成时间序列数据
    generateTimeSeriesData(alerts, timeRange) {
        const intervals = this.getTimeIntervals(timeRange);
        const data = intervals.map(interval => ({
            time: interval,
            count: 0,
            byLevel: { error: 0, warning: 0, info: 0 }
        }));
        
        alerts.forEach(alert => {
            const alertTime = new Date(alert.timestamp);
            const intervalIndex = this.findIntervalIndex(alertTime, intervals);
            
            if (intervalIndex >= 0) {
                data[intervalIndex].count++;
                if (data[intervalIndex].byLevel[alert.level] !== undefined) {
                    data[intervalIndex].byLevel[alert.level]++;
                }
            }
        });
        
        return data;
    }

    // 获取时间间隔
    getTimeIntervals(timeRange) {
        const now = new Date();
        const intervals = [];
        let intervalSize, intervalCount;
        
        switch (timeRange) {
            case '1h':
                intervalSize = 5 * 60 * 1000; // 5分钟
                intervalCount = 12;
                break;
            case '24h':
                intervalSize = 60 * 60 * 1000; // 1小时
                intervalCount = 24;
                break;
            case '7d':
                intervalSize = 24 * 60 * 60 * 1000; // 1天
                intervalCount = 7;
                break;
            case '30d':
                intervalSize = 24 * 60 * 60 * 1000; // 1天
                intervalCount = 30;
                break;
            default:
                intervalSize = 60 * 60 * 1000;
                intervalCount = 24;
        }
        
        for (let i = intervalCount - 1; i >= 0; i--) {
            intervals.push(new Date(now.getTime() - i * intervalSize));
        }
        
        return intervals;
    }

    // 查找时间间隔索引
    findIntervalIndex(alertTime, intervals) {
        for (let i = 0; i < intervals.length - 1; i++) {
            if (alertTime >= intervals[i] && alertTime < intervals[i + 1]) {
                return i;
            }
        }
        
        // 最后一个间隔
        if (alertTime >= intervals[intervals.length - 1]) {
            return intervals.length - 1;
        }
        
        return -1;
    }

    // 严重性分析
    getSeverityAnalysis() {
        return this.getCachedOrCompute('severityAnalysis', () => {
            const byLevel = this.groupByLevel();
            const total = this.alertHistory.length;
            
            if (total === 0) {
                return {
                    severityScore: 0,
                    riskLevel: 'low',
                    recommendations: ['系统运行正常，无警告记录']
                };
            }
            
            // 计算严重性分数 (错误=3分，警告=2分，信息=1分)
            const severityScore = (
                byLevel.error * 3 + 
                byLevel.warning * 2 + 
                byLevel.info * 1
            ) / total;
            
            let riskLevel, recommendations;
            
            if (severityScore >= 2.5) {
                riskLevel = 'high';
                recommendations = [
                    '系统存在大量错误级别警告，需要立即处理',
                    '检查传感器连接和配置',
                    '考虑调整警告阈值设置'
                ];
            } else if (severityScore >= 1.5) {
                riskLevel = 'medium';
                recommendations = [
                    '系统存在一定数量的警告，建议关注',
                    '定期检查传感器状态',
                    '优化系统配置以减少误报'
                ];
            } else {
                riskLevel = 'low';
                recommendations = [
                    '系统运行状态良好',
                    '继续保持当前监控策略',
                    '定期回顾警告设置'
                ];
            }
            
            return {
                severityScore: Math.round(severityScore * 100) / 100,
                riskLevel,
                recommendations,
                distribution: {
                    error: Math.round(byLevel.error / total * 100),
                    warning: Math.round(byLevel.warning / total * 100),
                    info: Math.round(byLevel.info / total * 100)
                }
            };
        });
    }

    // 频率分析
    getFrequencyAnalysis() {
        return this.getCachedOrCompute('frequencyAnalysis', () => {
            if (this.alertHistory.length === 0) {
                return {
                    averageInterval: 0,
                    peakHours: [],
                    quietHours: [],
                    burstDetection: []
                };
            }
            
            // 计算平均间隔
            const intervals = [];
            for (let i = 1; i < this.alertHistory.length; i++) {
                const interval = new Date(this.alertHistory[i-1].timestamp) - 
                               new Date(this.alertHistory[i].timestamp);
                intervals.push(Math.abs(interval));
            }
            
            const averageInterval = intervals.length > 0 ? 
                intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length : 0;
            
            // 分析高峰时段
            const hourlyDistribution = this.getHourlyDistribution();
            const peakHours = this.findPeakHours(hourlyDistribution);
            const quietHours = this.findQuietHours(hourlyDistribution);
            
            // 检测突发警告
            const burstDetection = this.detectAlertBursts();
            
            return {
                averageInterval: Math.round(averageInterval / 1000), // 转换为秒
                peakHours,
                quietHours,
                burstDetection,
                hourlyDistribution
            };
        });
    }

    // 获取小时分布
    getHourlyDistribution() {
        const distribution = new Array(24).fill(0);
        
        this.alertHistory.forEach(alert => {
            const hour = new Date(alert.timestamp).getHours();
            distribution[hour]++;
        });
        
        return distribution;
    }

    // 查找高峰时段
    findPeakHours(distribution) {
        const average = distribution.reduce((sum, count) => sum + count, 0) / 24;
        const threshold = average * 1.5; // 超过平均值50%视为高峰
        
        const peaks = [];
        distribution.forEach((count, hour) => {
            if (count > threshold) {
                peaks.push({ hour, count });
            }
        });
        
        return peaks.sort((a, b) => b.count - a.count);
    }

    // 查找安静时段
    findQuietHours(distribution) {
        const average = distribution.reduce((sum, count) => sum + count, 0) / 24;
        const threshold = average * 0.5; // 低于平均值50%视为安静
        
        const quiet = [];
        distribution.forEach((count, hour) => {
            if (count < threshold) {
                quiet.push({ hour, count });
            }
        });
        
        return quiet.sort((a, b) => a.count - b.count);
    }

    // 检测警告突发
    detectAlertBursts() {
        const bursts = [];
        const timeWindow = 5 * 60 * 1000; // 5分钟窗口
        const burstThreshold = 5; // 5分钟内超过5个警告视为突发
        
        for (let i = 0; i < this.alertHistory.length; i++) {
            const windowStart = new Date(this.alertHistory[i].timestamp);
            const windowEnd = new Date(windowStart.getTime() + timeWindow);
            
            let count = 0;
            for (let j = i; j < this.alertHistory.length; j++) {
                const alertTime = new Date(this.alertHistory[j].timestamp);
                if (alertTime >= windowStart && alertTime <= windowEnd) {
                    count++;
                } else if (alertTime > windowEnd) {
                    break;
                }
            }
            
            if (count >= burstThreshold) {
                bursts.push({
                    startTime: windowStart,
                    endTime: windowEnd,
                    alertCount: count
                });
            }
        }
        
        return bursts;
    }

    // 生成分析报告
    generateAnalysisReport() {
        const basicStats = this.getBasicStatistics();
        const timeTrend = this.getTimeTrendAnalysis('24h');
        const severityAnalysis = this.getSeverityAnalysis();
        const frequencyAnalysis = this.getFrequencyAnalysis();
        
        return {
            reportTime: new Date().toISOString(),
            summary: {
                totalAlerts: basicStats.total,
                riskLevel: severityAnalysis.riskLevel,
                averagePerDay: basicStats.averagePerDay,
                mostActiveNode: basicStats.mostActiveNode,
                mostProblematicSensor: basicStats.mostProblematicSensor
            },
            basicStatistics: basicStats,
            timeTrend,
            severityAnalysis,
            frequencyAnalysis,
            recommendations: this.generateRecommendations(basicStats, severityAnalysis, frequencyAnalysis)
        };
    }

    // 生成建议
    generateRecommendations(basicStats, severityAnalysis, frequencyAnalysis) {
        const recommendations = [...severityAnalysis.recommendations];
        
        // 基于频率分析的建议
        if (frequencyAnalysis.peakHours.length > 0) {
            recommendations.push(`高峰时段为${frequencyAnalysis.peakHours[0].hour}点，建议加强监控`);
        }
        
        if (frequencyAnalysis.burstDetection.length > 0) {
            recommendations.push('检测到警告突发事件，建议检查系统稳定性');
        }
        
        // 基于节点分析的建议
        if (basicStats.mostActiveNode) {
            recommendations.push(`节点${basicStats.mostActiveNode.nodeId}警告最多，建议重点检查`);
        }
        
        // 基于传感器分析的建议
        if (basicStats.mostProblematicSensor) {
            recommendations.push(`${basicStats.mostProblematicSensor.sensorType}传感器问题最多，建议校准或更换`);
        }
        
        return recommendations;
    }
}

// 导出警告分析工具
window.AlertAnalytics = AlertAnalytics;
