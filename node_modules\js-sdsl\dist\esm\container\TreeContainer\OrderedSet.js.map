{"version": 3, "sources": ["container/TreeContainer/OrderedSet.js", "../../src/container/TreeContainer/OrderedSet.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "__values", "o", "s", "m", "i", "TreeC<PERSON>r", "TreeIterator", "throwIteratorAccessError", "OrderedSetIterator", "_super", "node", "header", "container", "iteratorType", "_this", "defineProperty", "get", "_node", "_header", "_key", "enumerable", "configurable", "copy", "OrderedSet", "cmp", "enableIndex", "self", "for<PERSON>ach", "el", "insert", "_iterationFunc", "curNode", "_a", "undefined", "_left", "_right", "begin", "end", "rBegin", "rEnd", "front", "back", "key", "hint", "_set", "find", "element", "resNode", "_findElementNode", "_root", "lowerBound", "_lowerBound", "upperBound", "_upperBound", "reverseLowerBound", "_reverseLowerBound", "reverseUpperBound", "_reverseUpperBound", "union", "other", "_length"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;AACA,IAAIM,WAAY7C,QAAQA,KAAK6C,KAAa,SAASC;IAC/C,IAAIC,WAAWd,WAAW,cAAcA,OAAOC,UAAUc,IAAID,KAAKD,EAAEC,IAAIE,IAAI;IAC5E,IAAID,GAAG,OAAOA,EAAErC,KAAKmC;IACrB,IAAIA,YAAYA,EAAEJ,WAAW,UAAU,OAAO;QAC1Cb,MAAM;YACF,IAAIiB,KAAKG,KAAKH,EAAEJ,QAAQI,SAAS;YACjC,OAAO;gBAAEN,OAAOM,KAAKA,EAAEG;gBAAMV,OAAOO;;AACxC;;IAEJ,MAAM,IAAIlC,UAAUmC,IAAI,4BAA4B;AACxD;;OCpDOG,mBAAmB;;OACnBC,kBAAkB;;SAGhBC,gCAA0B;;AAEnC,IAAAC,qBAAA,SAAAC;IAAoCvD,UAAAsD,oBAAAC;IAElC,SAAAD,mBACEE,GACAC,GACAC,GACAC;QAJF,IAAAC,IAMEL,EAAA3C,KAAAX,MAAMuD,GAAMC,GAAQE,MAAa1D;QACjC2D,EAAKF,YAAYA;QD8Cb,OAAOE;AACX;IC7CFvD,OAAAwD,eAAIP,mBAAA5C,WAAA,WAAO;QD+CLoD,KC/CN;YACE,IAAI7D,KAAK8D,MAAU9D,KAAK+D,GAAS;gBAC/BX;ADgDM;YC9CR,OAAOpD,KAAK8D,EAAME;ADgDd;QACAC,YAAY;QACZC,cAAc;;IChDpBb,mBAAA5C,UAAA0D,OAAA;QACE,OAAO,IAAId,mBACTrD,KAAK8D,GACL9D,KAAK+D,GACL/D,KAAKyD,WACLzD,KAAK0D;AD+CP;IC1CJ,OAAAL;AAAA,CA3BA,CAAoCF;;AA+BpC,IAAAiB,aAAA,SAAAd;IAA4BvD,UAAAqE,YAAAd;IAW1B,SAAAc,WACEX,GACAY,GACAC;QAFA,IAAAb,WAAA,GAAA;YAAAA,IAAA;AAAgC;QADlC,IAAAE,IAKEL,EAAA3C,KAAAX,MAAMqE,GAAKC,MAAYtE;QACvB,IAAMuE,IAAOZ;QACbF,EAAUe,SAAQ,SAAUC;YAC1BF,EAAKG,OAAOD;ADwCV;QACA,OAAOd;AACX;ICpCQS,WAAA3D,UAAAkE,IAAV,SACEC;QDwCI,OAAO3D,YAAYjB,OAAM,SAAU6E;YAC/B,QAAQA,EAAGxD;cACP,KAAK;gBCxCjB,IAAIuD,MAAYE,WAAW,OAAA,EAAA;gBAC3B,OAAA,EAAA,GAAAjC,SAAQ7C,KAAK2E,EAAeC,EAAQG;;cD2CxB,KAAK;gBC3CjBF,EAAAvD;gBACA,OAAA,EAAA,GAAMsD,EAAQZ;;cD6CF,KAAK;gBC7CjBa,EAAAvD;gBACA,OAAA,EAAA,GAAAuB,SAAQ7C,KAAK2E,EAAeC,EAAQI;;cD+CxB,KAAK;gBC/CjBH,EAAAvD;gBDiDgB,OAAO,EAAC;;AAEpB;AACJ;IClDF8C,WAAA3D,UAAAwE,QAAA;QACE,OAAO,IAAI5B,mBACTrD,KAAK+D,EAAQgB,KAAS/E,KAAK+D,GAC3B/D,KAAK+D,GACL/D;ADiDF;IC9CFoE,WAAA3D,UAAAyE,MAAA;QACE,OAAO,IAAI7B,mBAAsBrD,KAAK+D,GAAS/D,KAAK+D,GAAS/D;ADgD7D;IC9CFoE,WAAA3D,UAAA0E,SAAA;QACE,OAAO,IAAI9B,mBACTrD,KAAK+D,EAAQiB,KAAUhF,KAAK+D,GAC5B/D,KAAK+D,GACL/D,MAAI;AD6CN;ICzCFoE,WAAA3D,UAAA2E,OAAA;QACE,OAAO,IAAI/B,mBAAsBrD,KAAK+D,GAAS/D,KAAK+D,GAAS/D,MAAI;AD2CjE;ICzCFoE,WAAA3D,UAAA4E,QAAA;QACE,OAAOrF,KAAK+D,EAAQgB,IAAQ/E,KAAK+D,EAAQgB,EAAMf,IAAOc;AD2CtD;ICzCFV,WAAA3D,UAAA6E,OAAA;QACE,OAAOtF,KAAK+D,EAAQiB,IAAShF,KAAK+D,EAAQiB,EAAOhB,IAAOc;AD2CxD;IC9BFV,WAAA3D,UAAAiE,SAAA,SAAOa,GAAQC;QACb,OAAOxF,KAAKyF,EAAKF,GAAKT,WAAWU;AD2CjC;ICzCFpB,WAAA3D,UAAAiF,OAAA,SAAKC;QACH,IAAMC,IAAU5F,KAAK6F,EAAiB7F,KAAK8F,GAAOH;QAClD,OAAO,IAAItC,mBAAsBuC,GAAS5F,KAAK+D,GAAS/D;AD2CxD;ICzCFoE,WAAA3D,UAAAsF,aAAA,SAAWR;QACT,IAAMK,IAAU5F,KAAKgG,EAAYhG,KAAK8F,GAAOP;QAC7C,OAAO,IAAIlC,mBAAsBuC,GAAS5F,KAAK+D,GAAS/D;AD2CxD;ICzCFoE,WAAA3D,UAAAwF,aAAA,SAAWV;QACT,IAAMK,IAAU5F,KAAKkG,EAAYlG,KAAK8F,GAAOP;QAC7C,OAAO,IAAIlC,mBAAsBuC,GAAS5F,KAAK+D,GAAS/D;AD2CxD;ICzCFoE,WAAA3D,UAAA0F,oBAAA,SAAkBZ;QAChB,IAAMK,IAAU5F,KAAKoG,EAAmBpG,KAAK8F,GAAOP;QACpD,OAAO,IAAIlC,mBAAsBuC,GAAS5F,KAAK+D,GAAS/D;AD2CxD;ICzCFoE,WAAA3D,UAAA4F,oBAAA,SAAkBd;QAChB,IAAMK,IAAU5F,KAAKsG,EAAmBtG,KAAK8F,GAAOP;QACpD,OAAO,IAAIlC,mBAAsBuC,GAAS5F,KAAK+D,GAAS/D;AD2CxD;ICzCFoE,WAAA3D,UAAA8F,QAAA,SAAMC;QACJ,IAAMjC,IAAOvE;QACbwG,EAAMhC,SAAQ,SAAUC;YACtBF,EAAKG,OAAOD;AD2CV;QCzCJ,OAAOzE,KAAKyG;AD2CZ;ICzCFrC,WAAA3D,UAACwB,OAAOC,YAAR;QACE,OAAOlC,KAAK2E,EAAe3E,KAAK8F;AD2ChC;ICnCJ,OAAA1B;AAAA,CA9GA,CAA4BlB;;eAgHbkB", "file": "OrderedSet.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nvar OrderedSetIterator = /** @class */ (function (_super) {\n    __extends(OrderedSetIterator, _super);\n    function OrderedSetIterator(node, header, container, iteratorType) {\n        var _this = _super.call(this, node, header, iteratorType) || this;\n        _this.container = container;\n        return _this;\n    }\n    Object.defineProperty(OrderedSetIterator.prototype, \"pointer\", {\n        get: function () {\n            if (this._node === this._header) {\n                throwIteratorAccessError();\n            }\n            return this._node._key;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OrderedSetIterator.prototype.copy = function () {\n        return new OrderedSetIterator(this._node, this._header, this.container, this.iteratorType);\n    };\n    return OrderedSetIterator;\n}(TreeIterator));\nvar OrderedSet = /** @class */ (function (_super) {\n    __extends(OrderedSet, _super);\n    /**\n     * @param container - The initialization container.\n     * @param cmp - The compare function.\n     * @param enableIndex - Whether to enable iterator indexing function.\n     * @example\n     * new OrderedSet();\n     * new OrderedSet([0, 1, 2]);\n     * new OrderedSet([0, 1, 2], (x, y) => x - y);\n     * new OrderedSet([0, 1, 2], (x, y) => x - y, true);\n     */\n    function OrderedSet(container, cmp, enableIndex) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this, cmp, enableIndex) || this;\n        var self = _this;\n        container.forEach(function (el) {\n            self.insert(el);\n        });\n        return _this;\n    }\n    /**\n     * @internal\n     */\n    OrderedSet.prototype._iterationFunc = function (curNode) {\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    if (curNode === undefined)\n                        return [2 /*return*/];\n                    return [5 /*yield**/, __values(this._iterationFunc(curNode._left))];\n                case 1:\n                    _a.sent();\n                    return [4 /*yield*/, curNode._key];\n                case 2:\n                    _a.sent();\n                    return [5 /*yield**/, __values(this._iterationFunc(curNode._right))];\n                case 3:\n                    _a.sent();\n                    return [2 /*return*/];\n            }\n        });\n    };\n    OrderedSet.prototype.begin = function () {\n        return new OrderedSetIterator(this._header._left || this._header, this._header, this);\n    };\n    OrderedSet.prototype.end = function () {\n        return new OrderedSetIterator(this._header, this._header, this);\n    };\n    OrderedSet.prototype.rBegin = function () {\n        return new OrderedSetIterator(this._header._right || this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    OrderedSet.prototype.rEnd = function () {\n        return new OrderedSetIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    OrderedSet.prototype.front = function () {\n        return this._header._left ? this._header._left._key : undefined;\n    };\n    OrderedSet.prototype.back = function () {\n        return this._header._right ? this._header._right._key : undefined;\n    };\n    /**\n     * @description Insert element to set.\n     * @param key - The key want to insert.\n     * @param hint - You can give an iterator hint to improve insertion efficiency.\n     * @return The size of container after setting.\n     * @example\n     * const st = new OrderedSet([2, 4, 5]);\n     * const iter = st.begin();\n     * st.insert(1);\n     * st.insert(3, iter);  // give a hint will be faster.\n     */\n    OrderedSet.prototype.insert = function (key, hint) {\n        return this._set(key, undefined, hint);\n    };\n    OrderedSet.prototype.find = function (element) {\n        var resNode = this._findElementNode(this._root, element);\n        return new OrderedSetIterator(resNode, this._header, this);\n    };\n    OrderedSet.prototype.lowerBound = function (key) {\n        var resNode = this._lowerBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    };\n    OrderedSet.prototype.upperBound = function (key) {\n        var resNode = this._upperBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    };\n    OrderedSet.prototype.reverseLowerBound = function (key) {\n        var resNode = this._reverseLowerBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    };\n    OrderedSet.prototype.reverseUpperBound = function (key) {\n        var resNode = this._reverseUpperBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    };\n    OrderedSet.prototype.union = function (other) {\n        var self = this;\n        other.forEach(function (el) {\n            self.insert(el);\n        });\n        return this._length;\n    };\n    OrderedSet.prototype[Symbol.iterator] = function () {\n        return this._iterationFunc(this._root);\n    };\n    return OrderedSet;\n}(TreeContainer));\nexport default OrderedSet;\n", "import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { TreeNode } from './Base/TreeNode';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass OrderedSetIterator<K> extends TreeIterator<K, undefined> {\n  container: OrderedSet<K>;\n  constructor(\n    node: TreeNode<K, undefined>,\n    header: TreeNode<K, undefined>,\n    container: OrderedSet<K>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    return this._node._key!;\n  }\n  copy() {\n    return new OrderedSetIterator<K>(\n      this._node,\n      this._header,\n      this.container,\n      this.iteratorType\n    );\n  }\n  // @ts-ignore\n  equals(iter: OrderedSetIterator<K>): boolean;\n}\n\nexport type { OrderedSetIterator };\n\nclass OrderedSet<K> extends <PERSON>Container<K, undefined> {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedSet();\n   * new OrderedSet([0, 1, 2]);\n   * new OrderedSet([0, 1, 2], (x, y) => x - y);\n   * new OrderedSet([0, 1, 2], (x, y) => x - y, true);\n   */\n  constructor(\n    container: initContainer<K> = [],\n    cmp?: (x: K, y: K) => number,\n    enableIndex?: boolean\n  ) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.insert(el);\n    });\n  }\n  /**\n   * @internal\n   */\n  private * _iterationFunc(\n    curNode: TreeNode<K, undefined> | undefined\n  ): Generator<K, void> {\n    if (curNode === undefined) return;\n    yield * this._iterationFunc(curNode._left);\n    yield curNode._key!;\n    yield * this._iterationFunc(curNode._right);\n  }\n  begin() {\n    return new OrderedSetIterator<K>(\n      this._header._left || this._header,\n      this._header,\n      this\n    );\n  }\n  end() {\n    return new OrderedSetIterator<K>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedSetIterator<K>(\n      this._header._right || this._header,\n      this._header,\n      this,\n      IteratorType.REVERSE\n    );\n  }\n  rEnd() {\n    return new OrderedSetIterator<K>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    return this._header._left ? this._header._left._key : undefined;\n  }\n  back() {\n    return this._header._right ? this._header._right._key : undefined;\n  }\n  /**\n   * @description Insert element to set.\n   * @param key - The key want to insert.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const st = new OrderedSet([2, 4, 5]);\n   * const iter = st.begin();\n   * st.insert(1);\n   * st.insert(3, iter);  // give a hint will be faster.\n   */\n  insert(key: K, hint?: OrderedSetIterator<K>) {\n    return this._set(key, undefined, hint);\n  }\n  find(element: K) {\n    const resNode = this._findElementNode(this._root, element);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  lowerBound(key: K) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  upperBound(key: K) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  reverseLowerBound(key: K) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  reverseUpperBound(key: K) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  union(other: OrderedSet<K>) {\n    const self = this;\n    other.forEach(function (el) {\n      self.insert(el);\n    });\n    return this._length;\n  }\n  [Symbol.iterator]() {\n    return this._iterationFunc(this._root);\n  }\n  // @ts-ignore\n  eraseElementByIterator(iter: OrderedSetIterator<K>): OrderedSetIterator<K>;\n  // @ts-ignore\n  forEach(callback: (element: K, index: number, tree: OrderedSet<K>) => void): void;\n  // @ts-ignore\n  getElementByPos(pos: number): K;\n}\n\nexport default OrderedSet;\n"]}