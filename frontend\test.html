<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 龙芯智能终端管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>龙芯智能终端管理系统 - 测试页面</h1>
        
        <div class="status info">
            <strong>测试目的：</strong>验证系统各组件是否正常工作
        </div>
        
        <h2>基础测试</h2>
        <div id="basicTest" class="status">正在进行基础测试...</div>
        
        <h2>配置测试</h2>
        <div id="configTest" class="status">正在测试配置...</div>
        
        <h2>工具类测试</h2>
        <div id="utilsTest" class="status">正在测试工具类...</div>
        
        <h2>页面导航测试</h2>
        <button class="test-btn" onclick="testMainPage()">测试主页面</button>
        <button class="test-btn" onclick="testNodeDetail()">测试节点详情页</button>
        <button class="test-btn" onclick="testAlertSettings()">测试警告设置页</button>
        
        <div id="navigationTest" class="status info">点击上方按钮测试页面导航</div>
        
        <h2>错误日志</h2>
        <div id="errorLog" class="status info">暂无错误</div>
    </div>

    <script>
        // 全局错误捕获
        window.addEventListener('error', function(e) {
            logError(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`);
        });

        window.addEventListener('unhandledrejection', function(e) {
            logError(`Promise错误: ${e.reason}`);
        });

        function logError(message) {
            const errorLog = document.getElementById('errorLog');
            errorLog.className = 'status error';
            errorLog.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function updateStatus(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.innerHTML = message;
        }

        // 基础测试
        try {
            updateStatus('basicTest', '✅ 基础JavaScript功能正常');
        } catch (e) {
            updateStatus('basicTest', `❌ 基础JavaScript错误: ${e.message}`, 'error');
        }

        // 页面导航测试函数
        function testMainPage() {
            updateStatus('navigationTest', '正在跳转到主页面...', 'info');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }

        function testNodeDetail() {
            updateStatus('navigationTest', '正在跳转到节点详情页（节点1）...', 'info');
            setTimeout(() => {
                window.location.href = 'pages/node-detail.html?nodeId=1';
            }, 1000);
        }

        function testAlertSettings() {
            updateStatus('navigationTest', '正在跳转到警告设置页...', 'info');
            setTimeout(() => {
                window.location.href = 'pages/alert-settings.html';
            }, 1000);
        }

        console.log('测试页面加载完成');
    </script>

    <!-- 逐步加载和测试JavaScript文件 -->
    <script>
        console.log('开始加载配置文件...');
    </script>
    
    <script src="js/config.js"></script>
    
    <script>
        // 测试配置
        try {
            if (typeof CONFIG !== 'undefined') {
                updateStatus('configTest', '✅ CONFIG配置加载成功');
                console.log('CONFIG加载成功:', CONFIG);
            } else {
                updateStatus('configTest', '❌ CONFIG配置未定义', 'error');
            }
        } catch (e) {
            updateStatus('configTest', `❌ CONFIG配置错误: ${e.message}`, 'error');
        }
    </script>

    <script src="js/utils.js"></script>
    
    <script>
        // 测试工具类
        try {
            if (typeof Utils !== 'undefined') {
                updateStatus('utilsTest', '✅ Utils工具类加载成功');
                console.log('Utils加载成功:', Utils);
                
                // 测试一些工具函数
                const testTime = Utils.formatTime();
                console.log('时间格式化测试:', testTime);
                
            } else {
                updateStatus('utilsTest', '❌ Utils工具类未定义', 'error');
            }
        } catch (e) {
            updateStatus('utilsTest', `❌ Utils工具类错误: ${e.message}`, 'error');
        }
    </script>

    <script>
        // 显示环境信息
        const envInfo = `
            <h2>环境信息</h2>
            <div class="status info">
                <strong>浏览器:</strong> ${navigator.userAgent}<br>
                <strong>URL:</strong> ${window.location.href}<br>
                <strong>时间:</strong> ${new Date().toLocaleString()}<br>
                <strong>屏幕分辨率:</strong> ${screen.width}x${screen.height}
            </div>
        `;
        
        document.querySelector('.container').insertAdjacentHTML('beforeend', envInfo);
        
        console.log('所有测试完成');
    </script>
</body>
</html>
