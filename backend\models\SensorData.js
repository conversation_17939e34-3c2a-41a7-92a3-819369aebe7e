// 传感器数据模型 - 复用Qt项目数据结构
class SensorData {
    constructor(nodeId, temperature = 0, humidity = 0, light = 0, smoke = 0) {
        this.nodeId = nodeId; // 节点ID (1, 2, 3)
        this.temperature = temperature; // 温度值 (temp1-3)
        this.humidity = humidity; // 湿度值 (humi1-3)
        this.light = light; // 光照值 (light1-3)
        this.smoke = smoke; // 烟雾值 (smog1-3)
        this.timestamp = new Date(); // 时间戳
        this.status = true; // 数据状态 (复用flag1-3)
    }

    // 验证数据范围 - 严格按照Qt项目设置
    static validateRanges(data) {
        const ranges = {
            temperature: { min: 0, max: 50 }, // 温度0-50℃
            humidity: { min: 10, max: 500 }, // 湿度10-500%RH
            light: { min: 10, max: 800 }, // 光照10-800lm
            smoke: { min: 0, max: 600 } // 烟雾0-600mg
        };

        const errors = [];
        
        Object.keys(ranges).forEach(key => {
            const value = data[key];
            const range = ranges[key];
            
            if (typeof value !== 'number' || isNaN(value)) {
                errors.push(`${key} must be a valid number`);
            } else if (value < range.min || value > range.max) {
                errors.push(`${key} must be between ${range.min} and ${range.max}`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // 创建传感器数据实例
    static create(nodeId, sensorValues) {
        const { temperature, humidity, light, smoke } = sensorValues;
        
        // 验证节点ID
        if (!nodeId || ![1, 2, 3].includes(Number(nodeId))) {
            throw new Error('Invalid nodeId. Must be 1, 2, or 3');
        }

        // 验证传感器数据
        const validation = SensorData.validateRanges(sensorValues);
        if (!validation.isValid) {
            throw new Error(`Invalid sensor data: ${validation.errors.join(', ')}`);
        }

        return new SensorData(Number(nodeId), temperature, humidity, light, smoke);
    }

    // 转换为JSON格式
    toJSON() {
        return {
            nodeId: this.nodeId,
            temperature: Number(this.temperature.toFixed(1)),
            humidity: Number(this.humidity.toFixed(1)),
            light: Number(this.light.toFixed(0)),
            smoke: Number(this.smoke.toFixed(1)),
            timestamp: this.timestamp.toISOString(),
            status: this.status
        };
    }

    // 从Qt项目数据格式转换
    static fromQtFormat(qtData) {
        // Qt数据格式: "nodeNumber,light,temp,humi,smog"
        const parts = qtData.split(',');
        if (parts.length !== 5) {
            throw new Error('Invalid Qt data format');
        }

        const nodeId = parseInt(parts[0]);
        const light = parseFloat(parts[1]);
        const temperature = parseFloat(parts[2]);
        const humidity = parseFloat(parts[3]);
        const smoke = parseFloat(parts[4]);

        return SensorData.create(nodeId, { temperature, humidity, light, smoke });
    }

    // 转换为华为云IoT格式
    toHuaweiIoTFormat() {
        return {
            services: [{
                service_id: "SensorData",
                properties: {
                    temperature: this.temperature,
                    humidity: this.humidity,
                    light: this.light,
                    smoke: this.smoke
                },
                event_time: new Date().toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/, '') + 'Z'
            }]
        };
    }

    // 检查数据是否异常 - 复用Qt警告逻辑
    checkAlerts() {
        const alerts = [];
        
        // 温度异常检查
        if (this.temperature > 50 || this.temperature < 0) {
            alerts.push({
                type: 'temperature',
                level: 'error',
                message: `节点${this.nodeId}温度异常: ${this.temperature}℃`
            });
        }

        // 湿度异常检查
        if (this.humidity > 500 || this.humidity < 10) {
            alerts.push({
                type: 'humidity',
                level: 'error',
                message: `节点${this.nodeId}湿度异常: ${this.humidity}%RH`
            });
        }

        // 光照异常检查
        if (this.light > 800 || this.light < 10) {
            alerts.push({
                type: 'light',
                level: 'warning',
                message: `节点${this.nodeId}光照异常: ${this.light}lm`
            });
        }

        // 烟雾异常检查
        if (this.smoke > 600) {
            alerts.push({
                type: 'smoke',
                level: 'error',
                message: `节点${this.nodeId}烟雾浓度过高: ${this.smoke}mg`
            });
        }

        return alerts;
    }
}

module.exports = SensorData;
