// MQTT客户端实现 - 复用Qt项目华为云IoT配置
const mqtt = require('mqtt');
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');
const dataManager = require('./dataManager');

class MQTTClient extends EventEmitter {
    constructor() {
        super();
        this.client = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectInterval = 5000; // 5秒重连间隔
        this.config = null;
        this.messageCount = 0;
        this.lastMessageTime = null;
        this.connectionStats = {
            connectTime: null,
            disconnectTime: null,
            totalMessages: 0,
            errors: 0
        };
        
        this.loadConfig();
    }

    // 加载MQTT配置 - 复用Qt项目配置
    loadConfig() {
        try {
            const configPath = path.join(__dirname, '../../config/mqtt.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            this.config = JSON.parse(configData);
            console.log('✅ MQTT配置加载成功');
        } catch (error) {
            console.error('❌ MQTT配置加载失败:', error.message);
            throw new Error('MQTT配置文件不存在或格式错误');
        }
    }

    // 连接到华为云IoT平台
    connect() {
        if (this.isConnected) {
            console.log('⚠️ MQTT客户端已连接');
            return;
        }

        const { huaweiCloud } = this.config;
        
        // 构建连接URL - 复用Qt项目配置
        const connectUrl = `mqtts://${huaweiCloud.hostname}:${huaweiCloud.port}`;
        
        // 连接选项
        const options = {
            clientId: huaweiCloud.clientId,
            username: huaweiCloud.deviceId,
            password: huaweiCloud.deviceSecret,
            keepalive: huaweiCloud.keepAlive || 120,
            clean: true,
            reconnectPeriod: this.reconnectInterval,
            connectTimeout: 30000,
            rejectUnauthorized: true, // SSL验证
            protocol: 'mqtts'
        };

        console.log(`🔗 连接华为云IoT平台: ${connectUrl}`);
        console.log(`📱 设备ID: ${huaweiCloud.deviceId}`);

        this.client = mqtt.connect(connectUrl, options);
        this.setupEventHandlers();
    }

    // 设置事件处理器
    setupEventHandlers() {
        // 连接成功
        this.client.on('connect', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionStats.connectTime = new Date();
            
            console.log('✅ MQTT连接成功');
            this.emit('connected');
            
            // 订阅主题
            this.subscribeToTopics();
        });

        // 连接错误
        this.client.on('error', (error) => {
            this.connectionStats.errors++;
            console.error('❌ MQTT连接错误:', error.message);
            this.emit('error', error);
        });

        // 连接断开
        this.client.on('close', () => {
            this.isConnected = false;
            this.connectionStats.disconnectTime = new Date();
            console.log('📱 MQTT连接断开');
            this.emit('disconnected');
        });

        // 重连
        this.client.on('reconnect', () => {
            this.reconnectAttempts++;
            console.log(`🔄 MQTT重连中... (第${this.reconnectAttempts}次)`);
            
            if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                console.error('❌ MQTT重连次数超限，停止重连');
                this.client.end();
            }
        });

        // 离线
        this.client.on('offline', () => {
            console.log('📴 MQTT客户端离线');
            this.emit('offline');
        });

        // 接收消息
        this.client.on('message', (topic, message) => {
            this.handleMessage(topic, message);
        });
    }

    // 订阅主题
    subscribeToTopics() {
        const { huaweiCloud } = this.config;
        const subscribeTopics = [
            huaweiCloud.topics.subscribe,
            // 可以添加更多订阅主题
        ];

        subscribeTopics.forEach(topic => {
            this.client.subscribe(topic, { qos: 1 }, (error) => {
                if (error) {
                    console.error(`❌ 订阅主题失败 ${topic}:`, error.message);
                } else {
                    console.log(`✅ 订阅主题成功: ${topic}`);
                }
            });
        });
    }

    // 处理接收到的消息
    handleMessage(topic, message) {
        try {
            this.messageCount++;
            this.lastMessageTime = new Date();
            this.connectionStats.totalMessages++;

            const messageStr = message.toString();
            console.log(`📨 收到MQTT消息 [${topic}]: ${messageStr}`);

            // 解析华为云IoT消息格式
            const parsedMessage = JSON.parse(messageStr);
            
            // 处理不同类型的消息
            if (topic === this.config.huaweiCloud.topics.subscribe) {
                this.handlePropertySetMessage(parsedMessage);
            }

            this.emit('message', { topic, message: parsedMessage });
        } catch (error) {
            console.error('❌ 消息处理失败:', error.message);
            this.emit('messageError', error);
        }
    }

    // 处理属性设置消息
    handlePropertySetMessage(message) {
        // 华为云IoT属性设置消息处理
        if (message.services && Array.isArray(message.services)) {
            message.services.forEach(service => {
                if (service.service_id === 'SensorData' && service.properties) {
                    this.processSensorData(service.properties);
                }
            });
        }
    }

    // 处理传感器数据
    processSensorData(properties) {
        try {
            // 假设数据包含节点ID信息，这里简化处理
            const nodeId = properties.nodeId || 1; // 默认节点1
            
            const sensorData = {
                temperature: properties.temperature || 0,
                humidity: properties.humidity || 0,
                light: properties.light || 0,
                smoke: properties.smoke || 0
            };

            // 更新数据管理器
            dataManager.updateNodeData(nodeId, sensorData);
            
            console.log(`📊 更新节点${nodeId}数据:`, sensorData);
            this.emit('sensorData', { nodeId, data: sensorData });
        } catch (error) {
            console.error('❌ 传感器数据处理失败:', error.message);
        }
    }

    // 发布消息到华为云IoT
    publishMessage(data) {
        if (!this.isConnected) {
            console.error('❌ MQTT未连接，无法发布消息');
            return false;
        }

        const { huaweiCloud } = this.config;
        const topic = huaweiCloud.topics.publish;

        // 构建华为云IoT消息格式
        const message = {
            services: [{
                service_id: this.config.dataFormat.serviceId,
                properties: data,
                event_time: new Date().toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/, '') + 'Z'
            }]
        };

        const messageStr = JSON.stringify(message);
        
        this.client.publish(topic, messageStr, { qos: 1 }, (error) => {
            if (error) {
                console.error('❌ 消息发布失败:', error.message);
                this.emit('publishError', error);
            } else {
                console.log(`📤 消息发布成功 [${topic}]: ${messageStr}`);
                this.emit('published', { topic, message });
            }
        });

        return true;
    }

    // 发布传感器数据
    publishSensorData(nodeId, sensorData) {
        const data = {
            nodeId,
            temperature: sensorData.temperature,
            humidity: sensorData.humidity,
            light: sensorData.light,
            smoke: sensorData.smoke,
            timestamp: new Date().toISOString()
        };

        return this.publishMessage(data);
    }

    // 获取连接状态
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            messageCount: this.messageCount,
            lastMessageTime: this.lastMessageTime,
            connectionStats: this.connectionStats,
            config: {
                deviceId: this.config?.huaweiCloud?.deviceId,
                hostname: this.config?.huaweiCloud?.hostname,
                port: this.config?.huaweiCloud?.port
            }
        };
    }

    // 断开连接
    disconnect() {
        if (this.client) {
            console.log('🔌 断开MQTT连接');
            this.client.end();
            this.isConnected = false;
            this.emit('disconnected');
        }
    }

    // 重新连接
    reconnect() {
        this.disconnect();
        setTimeout(() => {
            this.connect();
        }, 1000);
    }
}

module.exports = MQTTClient;
