{"version": 3, "file": "ali.js", "sourceRoot": "", "sources": ["../../../src/lib/connect/ali.ts"], "names": [], "mappings": ";;AAAA,mCAA+B;AAC/B,qDAA2C;AAI3C,sDAAkD;AAElD,IAAI,EAAO,CAAA;AACX,IAAI,KAAgB,CAAA;AACpB,IAAI,MAAsB,CAAA;AAC1B,IAAI,aAAa,GAAG,KAAK,CAAA;AAEzB,SAAS,UAAU;IAClB,MAAM,MAAM,GAAG,IAAI,2BAAS,EAAE,CAAA;IAC9B,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;QACzC,EAAE,CAAC,iBAAiB,CAAC;YACpB,IAAI,EAAE,KAAK,CAAC,MAAM;YAClB,OAAO;gBACN,IAAI,EAAE,CAAA;YACP,CAAC;YACD,IAAI;gBACH,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,CAAA;YAClB,CAAC;SACD,CAAC,CAAA;IACH,CAAC,CAAA;IACD,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,EAAE;QACxB,EAAE,CAAC,WAAW,CAAC;YACd,OAAO;gBACN,IAAI,EAAE,CAAA;YACP,CAAC;SACD,CAAC,CAAA;IACH,CAAC,CAAA;IAED,OAAO,MAAM,CAAA;AACd,CAAC;AAED,SAAS,cAAc,CAAC,IAAoB;IAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAA;IAC5B,CAAC;IACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAA;IAChB,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;IACpB,CAAC;AACF,CAAC;AAED,SAAS,QAAQ,CAAC,IAAoB,EAAE,MAAkB;IACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;IACxD,IAAI,GAAG,GAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;IACtD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;QACxD,GAAG,GAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;IAChE,CAAC;IACD,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,UAAU,EAAE,CAAC;QAC/C,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IAC7C,CAAC;IACD,OAAO,GAAG,CAAA;AACX,CAAC;AAED,SAAS,gBAAgB;IACxB,IAAI,aAAa;QAAE,OAAM;IAEzB,aAAa,GAAG,IAAI,CAAA;IAEpB,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;QACpB,MAAM,CAAC,WAAW,EAAE,CAAA;IACrB,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,eAAe,CAAC,CAAC,GAAG,EAAE,EAAE;QAC1B,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,eAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAC9C,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnB,CAAC;aAAM,CAAC;YACP,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAA;YAC/B,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;gBACpC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAA;gBAExB,IAAI,IAAI,YAAY,WAAW;oBAAE,IAAI,GAAG,eAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;;oBACpD,IAAI,GAAG,eAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;gBACrC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACjB,CAAC,CAAC,CAAA;YACF,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACnC,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE;QACrB,MAAM,CAAC,GAAG,EAAE,CAAA;QACZ,MAAM,CAAC,OAAO,EAAE,CAAA;IACjB,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,EAAE;QACxB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IACpB,CAAC,CAAC,CAAA;AACH,CAAC;AAED,MAAM,WAAW,GAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;IACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAA;IAE1C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAA;IACpE,CAAC;IAED,MAAM,oBAAoB,GACzB,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC;QACzD,CAAC,CAAC,UAAU;QACZ,CAAC,CAAC,MAAM,CAAA;IAEV,cAAc,CAAC,IAAI,CAAC,CAAA;IAEpB,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAClC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;IAEZ,EAAE,CAAC,aAAa,CAAC;QAChB,GAAG;QACH,SAAS,EAAE,oBAAoB;KAC/B,CAAC,CAAA;IAEF,KAAK,GAAG,UAAU,EAAE,CAAA;IACpB,MAAM,GAAG,IAAI,+BAAc,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IAE5C,gBAAgB,EAAE,CAAA;IAElB,OAAO,MAAM,CAAA;AACd,CAAC,CAAA;AAED,kBAAe,WAAW,CAAA"}