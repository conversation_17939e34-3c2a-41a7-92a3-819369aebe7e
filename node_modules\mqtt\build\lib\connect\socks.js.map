{"version": 3, "file": "socks.js", "sourceRoot": "", "sources": ["../../../src/lib/connect/socks.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgNA,4BA2BC;AA3OD,kDAA0B;AAC1B,mCAA+B;AAC/B,iCAAoD;AACpD,yCAA0B;AAG1B,+BAAgC;AAEhC,oDAA2B;AAE3B,MAAM,KAAK,GAAG,IAAA,eAAM,EAAC,cAAc,CAAC,CAAA;AAOpC,MAAM,WAAY,SAAQ,eAAM;IAK/B;QACC,KAAK,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAA;QALtB,aAAQ,GAAG,KAAK,CAAA;QA2DhB,YAAO,GAAG,CAAC,KAAU,EAAQ,EAAE;YACtC,IAAA,gBAAM,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAEpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;QACzC,CAAC,CAAA;QAEO,WAAM,GAAG,GAAS,EAAE;YAC3B,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAElC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChB,CAAC,CAAA;QAEO,aAAQ,GAAG,GAAS,EAAE;YAC7B,KAAK,CAAC,qBAAqB,CAAC,CAAA;YAE5B,IAAI,CAAC,OAAO,EAAE,CAAA;QACf,CAAC,CAAA;QAEO,aAAQ,GAAG,CAAC,GAAQ,EAAQ,EAAE;YACrC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAA;YAE7C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QAClB,CAAC,CAAA;QA3EA,IAAI,CAAC,IAAI,EAAE,CAAA;IACZ,CAAC;IAED,MAAM,CAAC,MAAc;QACpB,KAAK,CAAC,sBAAsB,CAAC,CAAA;QAE7B,IAAA,gBAAM,EAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAErB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC5B,OAAM;QACP,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QAErB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,MAAM,CAAC,KAAK,EAAE,CAAA;QAElC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAC/B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAC7B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QACjC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAEjC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAEtB,IAAI,CAAC,MAAM,EAAE,CAAA;IACd,CAAC;IAED,MAAM,CACL,KAAU,EACV,QAAwB,EACxB,QAAwC;QAExC,IAAA,gBAAM,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAEpB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IACpC,CAAC;IAED,KAAK,CAAC,IAAY;;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QAEpB,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,kDAAI,CAAA;IACzB,CAAC;IAED,QAAQ,CACP,KAAmB,EACnB,QAAwC;;QAExC,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,OAAO,mDAAG,KAAK,CAAC,CAAA;QAE9B,QAAQ,CAAC,KAAK,CAAC,CAAA;IAChB,CAAC;CA0BD;AAED,SAAS,KAAK,CAAI,CAAI;IACrB,IAAI,CAAC;QACJ,IAAK,CAAS,CAAC,IAAI,KAAK,SAAS;YAAG,CAAS,CAAC,IAAI,GAAG,OAAO,CAAA;QAC5D,OAAO,CAAC,CAAA;IACT,CAAC;IAAC,WAAM,CAAC;QACR,OAAO,CAAC,CAAA;IACT,CAAC;AACF,CAAC;AAED,SAAS,gBAAgB,CACxB,KAAa;IAEb,QAAQ,KAAK,EAAE,CAAC;QACf,KAAK,UAAU;YACd,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QAEjB,KAAK,UAAU;YACd,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QAEjB,KAAK,SAAS;YACb,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAElB,KAAK,SAAS;YACb,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAElB;YACC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;IAC3B,CAAC;AACF,CAAC;AAED,SAAS,aAAa,CAAC,GAAW;IACjC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;IAE9B,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9D,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAA;IACxC,CAAC;IAED,MAAM,CAAC,IAAI,EAAE,mBAAmB,CAAC,GAAG,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IACxE,IAAI,CAAC,IAAI,EAAE,CAAC;QACX,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IACzC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAA;IACtD,CAAC;IAED,MAAM,KAAK,GAAe;QACzB,IAAI,EAAE,SAAS,CAAC,QAAQ;QACxB,IAAI;QACJ,IAAI;KACJ,CAAA;IAED,OAAO,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAA;AACpC,CAAC;AAED,KAAK,UAAU,YAAY,CAC1B,eAAuB,EACvB,eAAuB,EACvB,QAAgB,EAChB,MAAmB,EACnB,UAAkC,EAAE;;IAEpC,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,IAAA,gBAAS,EAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAEtD,MAAM,CAAC,KAAK,EAAE,mBAAmB,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAA;IAE5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1B,KAAK,CAAC,sBAAsB,EAAE,eAAe,CAAC,CAAA;QAE9C,eAAe,GAAG,CACjB,MAAM,MAAM,CAAC,eAAe,EAAE;YAC7B,MAAM,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChC,CAAC,CACF,CAAC,OAAO,CAAA;IACV,CAAC;IAED,KAAK,CACJ,oDAAoD,EACpD,KAAK,CAAC,IAAI,EACV,eAAe,EACf,eAAe,EACf,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,IAAI,CACV,CAAA;IAED,MAAM,WAAW,GAAG,IAAI,mBAAW,CAAC;QACnC,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE;YACZ,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,eAAe;SACrB;QACD,KAAK,oBAAO,KAAK,CAAE;QACnB,OAAO,EAAE,OAAO,CAAC,OAAO;KACxB,CAAC,CAAA;IACF,WAAW,CAAC,OAAO,EAAE,CAAA;IAErB,WAAW,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;IAEpE,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;QAC7B,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAA;QAC5B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACzB,CAAC,CAAC,CAAA;AACH,CAAC;AAED,SAAwB,SAAS,CAChC,eAAuB,EACvB,eAAuB,EACvB,QAAgB,EAChB,OAAgC;IAEhC,KAAK,CACJ,kCAAkC,EAClC,eAAe,EACf,eAAe,EACf,QAAQ,CACR,CAAA;IAED,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;IAEhC,YAAY,CACX,eAAe,EACf,eAAe,EACf,QAAQ,EACR,MAAM,EACN,OAAO,CACP,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAA;QAC5B,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAClB,CAAC,CAAC,CAAA;IAEF,OAAO,MAAM,CAAA;AACd,CAAC"}