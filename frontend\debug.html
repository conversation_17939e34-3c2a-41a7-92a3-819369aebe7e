<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面 - 龙芯智能终端管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>龙芯智能终端管理系统 - 调试页面</h1>
    
    <div class="debug-section">
        <h2>JavaScript加载测试</h2>
        <div id="jsLoadStatus">正在检查JavaScript文件...</div>
    </div>
    
    <div class="debug-section">
        <h2>配置检查</h2>
        <div id="configStatus">正在检查配置...</div>
    </div>
    
    <div class="debug-section">
        <h2>工具类检查</h2>
        <div id="utilsStatus">正在检查工具类...</div>
    </div>
    
    <div class="debug-section">
        <h2>WebSocket连接测试</h2>
        <div id="wsStatus">正在测试WebSocket连接...</div>
    </div>
    
    <div class="debug-section">
        <h2>错误日志</h2>
        <div id="errorLog">暂无错误</div>
    </div>

    <script>
        // 错误捕获
        window.addEventListener('error', function(e) {
            const errorLog = document.getElementById('errorLog');
            errorLog.innerHTML += `<div class="error">错误: ${e.message} (${e.filename}:${e.lineno})</div>`;
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(e) {
            const errorLog = document.getElementById('errorLog');
            errorLog.innerHTML += `<div class="error">Promise错误: ${e.reason}</div>`;
        });

        // 测试函数
        function updateStatus(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }

        // 开始测试
        console.log('开始调试测试...');
        
        // 1. 测试基础JavaScript
        try {
            updateStatus('jsLoadStatus', '✅ 基础JavaScript正常');
        } catch (e) {
            updateStatus('jsLoadStatus', `❌ 基础JavaScript错误: ${e.message}`, true);
        }
    </script>

    <!-- 逐步加载JavaScript文件进行测试 -->
    <script>
        console.log('开始加载配置文件...');
    </script>
    
    <script src="js/config.js"></script>
    
    <script>
        // 测试配置
        try {
            if (typeof CONFIG !== 'undefined') {
                updateStatus('configStatus', '✅ CONFIG配置加载成功');
                console.log('CONFIG:', CONFIG);
            } else {
                updateStatus('configStatus', '❌ CONFIG配置未定义', true);
            }
        } catch (e) {
            updateStatus('configStatus', `❌ CONFIG配置错误: ${e.message}`, true);
        }
    </script>

    <script src="js/utils.js"></script>
    
    <script>
        // 测试工具类
        try {
            if (typeof Utils !== 'undefined') {
                updateStatus('utilsStatus', '✅ Utils工具类加载成功');
                console.log('Utils:', Utils);
            } else {
                updateStatus('utilsStatus', '❌ Utils工具类未定义', true);
            }
        } catch (e) {
            updateStatus('utilsStatus', `❌ Utils工具类错误: ${e.message}`, true);
        }
    </script>

    <script src="/socket.io/socket.io.js"></script>
    
    <script>
        // 测试Socket.io
        try {
            if (typeof io !== 'undefined') {
                updateStatus('wsStatus', '✅ Socket.io加载成功，正在测试连接...');
                
                // 测试WebSocket连接
                const socket = io();
                
                socket.on('connect', function() {
                    updateStatus('wsStatus', '✅ WebSocket连接成功');
                });
                
                socket.on('connect_error', function(error) {
                    updateStatus('wsStatus', `❌ WebSocket连接失败: ${error.message}`, true);
                });
                
                // 5秒后如果还没连接成功，显示超时
                setTimeout(() => {
                    if (!socket.connected) {
                        updateStatus('wsStatus', '❌ WebSocket连接超时', true);
                    }
                }, 5000);
                
            } else {
                updateStatus('wsStatus', '❌ Socket.io未加载', true);
            }
        } catch (e) {
            updateStatus('wsStatus', `❌ WebSocket测试错误: ${e.message}`, true);
        }
    </script>

    <script>
        console.log('调试页面加载完成');
        
        // 显示浏览器信息
        const debugInfo = `
            <div class="info">
                <strong>浏览器信息:</strong><br>
                User Agent: ${navigator.userAgent}<br>
                URL: ${window.location.href}<br>
                时间: ${new Date().toLocaleString()}
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', `
            <div class="debug-section">
                <h2>环境信息</h2>
                ${debugInfo}
            </div>
        `);
    </script>
</body>
</html>
