// 节点相关API路由
const express = require('express');
const router = express.Router();
const dataManager = require('../services/dataManager');
const ResponseHandler = require('../middleware/responseHandler');
const { asyncHandler } = require('../middleware/errorHandler');
const DataValidator = require('../utils/dataValidator');

// GET /api/nodes - 获取所有节点状态
router.get('/', asyncHandler(async (req, res) => {
    const nodes = dataManager.getAllNodes();
    return ResponseHandler.success(res, nodes, '成功获取所有节点状态');
}));

// GET /api/nodes/:id - 获取单个节点详细信息
router.get('/:id', asyncHandler(async (req, res) => {
    const nodeId = req.params.id;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    const node = dataManager.getNode(validation.value);
    if (!node) {
        return ResponseHandler.notFound(res, `节点${validation.value}`);
    }
    
    return ResponseHandler.success(res, node, `成功获取节点${validation.value}信息`);
}));

// GET /api/nodes/:id/data - 获取节点当前传感器数据
router.get('/:id/data', asyncHandler(async (req, res) => {
    const nodeId = req.params.id;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    const data = dataManager.getNodeCurrentData(validation.value);
    if (!data) {
        return ResponseHandler.notFound(res, `节点${validation.value}的传感器数据`);
    }
    
    return ResponseHandler.success(res, data, `成功获取节点${validation.value}当前数据`);
}));

// GET /api/nodes/:id/history - 获取节点历史数据
router.get('/:id/history', asyncHandler(async (req, res) => {
    const nodeId = req.params.id;
    const hours = parseInt(req.query.hours) || 1;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    // 验证分页参数
    const paginationValidation = DataValidator.validatePagination(page, limit);
    if (!paginationValidation.isValid) {
        return ResponseHandler.validationError(res, paginationValidation.error);
    }
    
    // 验证时间范围
    if (hours < 1 || hours > 168) { // 最多7天
        return ResponseHandler.validationError(res, '时间范围必须在1-168小时之间');
    }
    
    const history = dataManager.getNodeHistory(validation.value, hours);
    
    // 分页处理
    const total = history.length;
    const startIndex = paginationValidation.offset;
    const endIndex = startIndex + paginationValidation.limit;
    const paginatedData = history.slice(startIndex, endIndex);
    
    return ResponseHandler.paginated(res, paginatedData, {
        page: paginationValidation.page,
        limit: paginationValidation.limit,
        total
    }, `成功获取节点${validation.value}历史数据`);
}));

// POST /api/nodes/:id/data - 更新节点传感器数据
router.post('/:id/data', asyncHandler(async (req, res) => {
    const nodeId = req.params.id;
    const sensorData = req.body;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    // 验证请求体
    const requestValidation = DataValidator.validateApiRequest(req, ['temperature', 'humidity', 'light', 'smoke']);
    if (!requestValidation.isValid) {
        return ResponseHandler.validationError(res, requestValidation.errors);
    }
    
    // 验证传感器数据
    const dataValidation = DataValidator.validateSensorData(sensorData);
    if (!dataValidation.isValid) {
        return ResponseHandler.validationError(res, dataValidation.errors);
    }
    
    // 清理数据
    const sanitizedData = DataValidator.sanitizeData(sensorData);
    
    // 更新数据
    const result = dataManager.updateNodeData(validation.value, sanitizedData);
    
    return ResponseHandler.success(res, result, `成功更新节点${validation.value}数据`, 201);
}));

// POST /api/nodes/qt-data - 接收Qt格式数据
router.post('/qt-data', asyncHandler(async (req, res) => {
    const { data } = req.body;
    
    if (!data || typeof data !== 'string') {
        return ResponseHandler.validationError(res, 'Qt数据格式错误，需要字符串格式');
    }
    
    // 验证Qt格式
    const validation = DataValidator.validateQtFormat(data);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    // 更新数据
    const result = dataManager.updateFromQtFormat(data);
    
    return ResponseHandler.success(res, result, '成功处理Qt格式数据', 201);
}));

// PUT /api/nodes/:id/reset - 重置节点数据
router.put('/:id/reset', asyncHandler(async (req, res) => {
    const nodeId = req.params.id;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    const result = dataManager.resetNode(validation.value);
    
    return ResponseHandler.success(res, result, `成功重置节点${validation.value}`);
}));

// GET /api/nodes/:id/summary - 获取节点摘要信息
router.get('/:id/summary', asyncHandler(async (req, res) => {
    const nodeId = req.params.id;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    const node = dataManager.getNode(validation.value);
    if (!node) {
        return ResponseHandler.notFound(res, `节点${validation.value}`);
    }
    
    // 返回摘要信息
    const summary = {
        id: node.id,
        name: node.name,
        status: node.status,
        alertLevel: node.alertLevel,
        lastSeen: node.lastSeen,
        isOffline: node.isOffline,
        dataCount: node.dataCount,
        alertCount: node.alertCount
    };
    
    return ResponseHandler.success(res, summary, `成功获取节点${validation.value}摘要`);
}));

module.exports = router;
