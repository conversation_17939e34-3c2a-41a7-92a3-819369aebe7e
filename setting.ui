<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>setting</class>
 <widget class="QWidget" name="setting">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>802</width>
    <height>563</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>设置</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QPushButton" name="backButton">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>70</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">border-image: url(:/back.png);</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>180</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="2">
    <widget class="QLabel" name="label_time">
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="3">
    <spacer name="verticalSpacer_3">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>126</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="4">
    <spacer name="horizontalSpacer_6">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>179</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0">
    <spacer name="horizontalSpacer_4">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>180</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="1" colspan="4">
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_10">
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="font">
          <font>
           <family>Microsoft YaHei UI</family>
           <pointsize>16</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>报警值设置</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_8">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>温度上限值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDoubleSpinBox" name="doubleSpinBox_tempMax">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>0</height>
              </size>
             </property>
             <property name="value">
              <double>40.000000000000000</double>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>温度下限值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDoubleSpinBox" name="doubleSpinBox_tempMin">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>0</height>
              </size>
             </property>
             <property name="value">
              <double>10.000000000000000</double>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>湿度上限值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDoubleSpinBox" name="doubleSpinBox_humiMax">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>0</height>
              </size>
             </property>
             <property name="value">
              <double>90.000000000000000</double>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>湿度下限值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDoubleSpinBox" name="doubleSpinBox_humiMin">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>0</height>
              </size>
             </property>
             <property name="value">
              <double>10.000000000000000</double>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_9">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <widget class="QLabel" name="label_6">
             <property name="text">
              <string>光照上限值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDoubleSpinBox" name="doubleSpinBox_lightMax">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximum">
              <double>1000.000000000000000</double>
             </property>
             <property name="value">
              <double>600.000000000000000</double>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <item>
            <widget class="QLabel" name="label_7">
             <property name="text">
              <string>光照下限值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QDoubleSpinBox" name="doubleSpinBox_lightMin">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximum">
              <double>1000.000000000000000</double>
             </property>
             <property name="value">
              <double>10.000000000000000</double>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>烟雾报警值</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDoubleSpinBox" name="doubleSpinBox_smogMax">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximum">
            <double>1000.000000000000000</double>
           </property>
           <property name="value">
            <double>800.000000000000000</double>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="1" column="5">
    <spacer name="horizontalSpacer_5">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>180</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="3">
    <spacer name="verticalSpacer_4">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>126</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
