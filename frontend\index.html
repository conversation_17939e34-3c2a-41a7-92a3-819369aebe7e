<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙芯智能终端管理系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 加载屏幕 -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="logo-icon">🖥️</div>
                <h2>龙芯终端管理系统</h2>
            </div>
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            <div class="loading-text">正在初始化系统...</div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="app-container" id="appContainer">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <h1>龙芯终端管理系统</h1>
                    <span class="version">Web v1.0</span>
                </div>
                <div class="header-info">
                    <div class="theme-toggle" id="themeToggle">
                        <span class="theme-icon">🌙</span>
                    </div>
                    <div class="time-display" id="currentTime"></div>
                    <div class="connection-status" id="connectionStatus">
                        <span class="status-dot"></span>
                        <span class="status-text">连接中...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 节点状态卡片区域 -->
            <section class="nodes-section">
                <h2 class="section-title">节点状态监控</h2>
                <div class="nodes-grid" id="nodesGrid">
                    <!-- 节点卡片将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 数据可视化区域 -->
            <section class="charts-section">
                <h2 class="section-title">实时数据图表</h2>

                <!-- 图表切换控制 -->
                <div class="chart-controls">
                    <button class="chart-tab active" data-chart="temperature">温度</button>
                    <button class="chart-tab" data-chart="humidity">湿度</button>
                    <button class="chart-tab" data-chart="light">光照</button>
                    <button class="chart-tab" data-chart="smoke">烟雾</button>
                    <button class="chart-tab" data-chart="all">全部</button>
                </div>

                <!-- 图表容器 -->
                <div class="charts-grid">
                    <div class="chart-container active" id="temperatureContainer">
                        <canvas id="temperatureChart"></canvas>
                    </div>
                    <div class="chart-container" id="humidityContainer">
                        <canvas id="humidityChart"></canvas>
                    </div>
                    <div class="chart-container" id="lightContainer">
                        <canvas id="lightChart"></canvas>
                    </div>
                    <div class="chart-container" id="smokeContainer">
                        <canvas id="smokeChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- 警告通知区域 -->
            <section class="alerts-section">
                <h2 class="section-title">系统警告</h2>
                <div class="alerts-container" id="alertsContainer">
                    <!-- 警告信息将通过JavaScript动态生成 -->
                </div>
            </section>
        </main>
    </div>

    <!-- 加载脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/socket.io/socket.io.js"></script>
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/chartUtils.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/nodes.js"></script>
    <script src="js/alerts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
