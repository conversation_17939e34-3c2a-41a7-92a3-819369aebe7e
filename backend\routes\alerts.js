// 警告相关API路由
const express = require('express');
const router = express.Router();
const dataManager = require('../services/dataManager');
const ResponseHandler = require('../middleware/responseHandler');
const { asyncHandler } = require('../middleware/errorHandler');
const DataValidator = require('../utils/dataValidator');

// GET /api/alerts - 获取所有活跃警告
router.get('/', asyncHandler(async (req, res) => {
    const hours = parseInt(req.query.hours) || 24;
    const level = req.query.level; // info, warning, error
    const nodeId = req.query.nodeId;
    
    // 验证时间范围
    if (hours < 1 || hours > 168) {
        return ResponseHandler.validationError(res, '时间范围必须在1-168小时之间');
    }
    
    let alerts = dataManager.getAllActiveAlerts();
    
    // 按时间过滤
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    alerts = alerts.filter(alert => new Date(alert.timestamp) > cutoffTime);
    
    // 按级别过滤
    if (level && ['info', 'warning', 'error'].includes(level)) {
        alerts = alerts.filter(alert => alert.level === level);
    }
    
    // 按节点过滤
    if (nodeId) {
        const validation = DataValidator.validateNodeId(nodeId);
        if (!validation.isValid) {
            return ResponseHandler.validationError(res, validation.error);
        }
        alerts = alerts.filter(alert => alert.nodeId === validation.value);
    }
    
    return ResponseHandler.success(res, alerts, '成功获取警告信息');
}));

// GET /api/alerts/summary - 获取警告摘要统计
router.get('/summary', asyncHandler(async (req, res) => {
    const hours = parseInt(req.query.hours) || 24;
    
    // 验证时间范围
    if (hours < 1 || hours > 168) {
        return ResponseHandler.validationError(res, '时间范围必须在1-168小时之间');
    }
    
    const alerts = dataManager.getAllActiveAlerts();
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentAlerts = alerts.filter(alert => new Date(alert.timestamp) > cutoffTime);
    
    // 统计信息
    const summary = {
        total: recentAlerts.length,
        byLevel: {
            error: recentAlerts.filter(a => a.level === 'error').length,
            warning: recentAlerts.filter(a => a.level === 'warning').length,
            info: recentAlerts.filter(a => a.level === 'info').length
        },
        byNode: {},
        byType: {},
        timeRange: hours
    };
    
    // 按节点统计
    for (let i = 1; i <= 3; i++) {
        summary.byNode[i] = recentAlerts.filter(a => a.nodeId === i).length;
    }
    
    // 按类型统计
    const types = ['temperature', 'humidity', 'light', 'smoke'];
    types.forEach(type => {
        summary.byType[type] = recentAlerts.filter(a => a.type === type).length;
    });
    
    return ResponseHandler.success(res, summary, '成功获取警告摘要');
}));

// GET /api/alerts/config/:nodeId - 获取节点警告配置
router.get('/config/:nodeId', asyncHandler(async (req, res) => {
    const nodeId = req.params.nodeId;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    const config = dataManager.getAlertConfig(validation.value);
    if (!config) {
        return ResponseHandler.notFound(res, `节点${validation.value}的警告配置`);
    }
    
    return ResponseHandler.success(res, config, `成功获取节点${validation.value}警告配置`);
}));

// POST /api/alerts/config/:nodeId - 更新节点警告配置
router.post('/config/:nodeId', asyncHandler(async (req, res) => {
    const nodeId = req.params.nodeId;
    const configData = req.body;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    // 验证配置数据
    const configValidation = DataValidator.validateAlertConfig(configData);
    if (!configValidation.isValid) {
        return ResponseHandler.validationError(res, configValidation.errors);
    }
    
    const result = dataManager.updateAlertConfig(validation.value, configData);
    
    return ResponseHandler.success(res, result, `成功更新节点${validation.value}警告配置`, 201);
}));

// GET /api/alerts/config - 获取所有节点的警告配置
router.get('/config', asyncHandler(async (req, res) => {
    const configs = {};
    
    for (let i = 1; i <= 3; i++) {
        const config = dataManager.getAlertConfig(i);
        if (config) {
            configs[i] = config;
        }
    }
    
    return ResponseHandler.success(res, configs, '成功获取所有警告配置');
}));

// POST /api/alerts/test/:nodeId - 测试警告配置
router.post('/test/:nodeId', asyncHandler(async (req, res) => {
    const nodeId = req.params.nodeId;
    const testData = req.body;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    // 验证测试数据
    const dataValidation = DataValidator.validateSensorData(testData);
    if (!dataValidation.isValid) {
        return ResponseHandler.validationError(res, dataValidation.errors);
    }
    
    // 获取警告配置
    const alertConfig = dataManager.getAlertConfig(validation.value);
    if (!alertConfig) {
        return ResponseHandler.notFound(res, `节点${validation.value}的警告配置`);
    }
    
    // 创建临时传感器数据对象进行测试
    const SensorData = require('../models/SensorData');
    const tempSensorData = SensorData.create(validation.value, testData);
    
    // 检查警告
    const alerts = tempSensorData.checkAlerts();
    
    const result = {
        nodeId: validation.value,
        testData: tempSensorData.toJSON(),
        alerts: alerts,
        hasAlerts: alerts.length > 0,
        alertCount: alerts.length
    };
    
    return ResponseHandler.success(res, result, `成功测试节点${validation.value}警告配置`);
}));

// DELETE /api/alerts/history/:nodeId - 清除节点警告历史
router.delete('/history/:nodeId', asyncHandler(async (req, res) => {
    const nodeId = req.params.nodeId;
    
    // 验证节点ID
    const validation = DataValidator.validateNodeId(nodeId);
    if (!validation.isValid) {
        return ResponseHandler.validationError(res, validation.error);
    }
    
    // 获取节点
    const node = dataManager.getNode(validation.value);
    if (!node) {
        return ResponseHandler.notFound(res, `节点${validation.value}`);
    }
    
    // 清除警告历史（这里需要在NodeInfo模型中添加清除方法）
    // 暂时返回成功响应
    const result = {
        nodeId: validation.value,
        message: '警告历史已清除',
        clearedAt: new Date().toISOString()
    };
    
    return ResponseHandler.success(res, result, `成功清除节点${validation.value}警告历史`);
}));

// GET /api/alerts/latest - 获取最新警告
router.get('/latest', asyncHandler(async (req, res) => {
    const limit = parseInt(req.query.limit) || 10;
    
    if (limit < 1 || limit > 100) {
        return ResponseHandler.validationError(res, '限制数量必须在1-100之间');
    }
    
    const alerts = dataManager.getAllActiveAlerts();
    const latestAlerts = alerts.slice(0, limit);
    
    return ResponseHandler.success(res, latestAlerts, '成功获取最新警告');
}));

module.exports = router;
